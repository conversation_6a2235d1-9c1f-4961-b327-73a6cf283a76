# BloomBudget Deployment Guide

## Overview
This guide provides comprehensive instructions for deploying the BloomBudget application across different environments using modern DevOps practices.

## Architecture Overview

### Production Architecture
```
Internet → Load Balancer → Nginx → Backend Pods → MySQL/Redis
                                 ↓
                              Monitoring Stack
                              (Prometheus/Grafana)
```

### Components
- **Frontend**: Flutter mobile application
- **Backend**: FastAPI application running in Kubernetes
- **Database**: MySQL 8.0 with replication
- **Cache**: Redis for session and data caching
- **Monitoring**: Prometheus + Grafana + ELK Stack
- **Load Balancer**: Nginx with SSL termination

## Prerequisites

### System Requirements
- **Kubernetes Cluster**: v1.25+ (for production)
- **Docker**: v20.10+
- **kubectl**: v1.25+
- **Helm**: v3.10+ (optional)
- **Git**: v2.30+

### Access Requirements
- GitHub repository access
- Container registry access (GHCR)
- Kubernetes cluster admin access
- SSL certificates for production domain

## Environment Setup

### 1. Development Environment
```bash
# Clone repository
git clone https://github.com/alinma/bloom-budget.git
cd bloom-budget

# Start development environment
docker-compose up -d

# Verify services
curl http://localhost:8000/health
```

### 2. Staging Environment
```bash
# Deploy to staging
./scripts/deploy.sh staging

# Monitor deployment
docker-compose logs -f backend
```

### 3. Production Environment
```bash
# Deploy to production (requires approval)
./scripts/deploy.sh production

# Monitor deployment
kubectl get pods -n bloom-budget -w
```

## Deployment Methods

### Method 1: Automated CI/CD (Recommended)

#### GitHub Actions Pipeline
The CI/CD pipeline automatically:
1. Runs tests on pull requests
2. Builds and pushes Docker images
3. Deploys to staging on `develop` branch
4. Deploys to production on `main` branch

#### Required Secrets
Configure these secrets in GitHub repository settings:

```bash
# Production server access
PRODUCTION_HOST=your-production-server.com
PRODUCTION_USER=deploy
PRODUCTION_SSH_KEY=<private-key>

# Container registry
GITHUB_TOKEN=<github-token>

# Notifications
SLACK_WEBHOOK_URL=<slack-webhook>

# External services
OPENAI_API_KEY=<openai-key>
```

### Method 2: Manual Deployment

#### Quick Deployment
```bash
# Build and deploy to staging
./scripts/deploy.sh staging

# Deploy to production with confirmation
./scripts/deploy.sh production
```

#### Advanced Options
```bash
# Build only (no deployment)
./scripts/deploy.sh -b

# Deploy only (skip build)
./scripts/deploy.sh -d production

# Force deployment (skip confirmation)
./scripts/deploy.sh -f production

# Rollback to previous version
./scripts/deploy.sh -r production
```

## Kubernetes Deployment

### 1. Cluster Setup
```bash
# Create namespace
kubectl apply -f k8s/namespace.yaml

# Apply configurations
kubectl apply -f k8s/configmap.yaml

# Deploy MySQL
kubectl apply -f k8s/mysql-deployment.yaml

# Deploy Redis
kubectl apply -f k8s/redis-deployment.yaml

# Deploy backend
kubectl apply -f k8s/backend-deployment.yaml

# Deploy ingress
kubectl apply -f k8s/ingress.yaml
```

### 2. Verify Deployment
```bash
# Check pod status
kubectl get pods -n bloom-budget

# Check services
kubectl get services -n bloom-budget

# Check ingress
kubectl get ingress -n bloom-budget

# View logs
kubectl logs -f deployment/backend-deployment -n bloom-budget
```

### 3. Scaling
```bash
# Scale backend pods
kubectl scale deployment backend-deployment --replicas=5 -n bloom-budget

# Enable auto-scaling
kubectl apply -f k8s/hpa.yaml
```

## Database Management

### Initial Setup
```bash
# Run database migrations
kubectl exec -it deployment/backend-deployment -n bloom-budget -- \
  python -m alembic upgrade head

# Create initial admin user
kubectl exec -it deployment/backend-deployment -n bloom-budget -- \
  python scripts/create_admin.py
```

### Backup and Restore
```bash
# Create backup
./scripts/backup.sh

# List available backups
ls -la /opt/backups/bloom-budget/

# Restore from backup
./scripts/restore.sh 20231201_143000

# Restore database only
./scripts/restore.sh -d 20231201_143000
```

## Monitoring and Logging

### Prometheus Metrics
Access Prometheus at: `http://your-domain:9090`

Key metrics to monitor:
- `http_requests_total` - API request count
- `http_request_duration_seconds` - Response times
- `mysql_up` - Database connectivity
- `redis_up` - Cache connectivity

### Grafana Dashboards
Access Grafana at: `http://your-domain:3000`
- Default credentials: admin/admin123

Pre-configured dashboards:
- Application Performance
- Infrastructure Metrics
- Business Metrics (user activity, savings)

### Log Management
Access Kibana at: `http://your-domain:5601`

Log sources:
- Application logs (JSON format)
- Nginx access logs
- System logs

## Security Configuration

### SSL/TLS Setup
```bash
# Generate SSL certificate (Let's Encrypt)
certbot certonly --nginx -d bloombudget.alinma.com

# Update nginx configuration
cp nginx/nginx.conf /etc/nginx/sites-available/bloombudget
nginx -t && systemctl reload nginx
```

### Security Headers
The nginx configuration includes:
- HSTS headers
- Content Security Policy
- X-Frame-Options
- X-Content-Type-Options

### Network Security
- All services run in private networks
- Database access restricted to application pods
- Rate limiting enabled on API endpoints

## Performance Optimization

### Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_savings_goals_user_id ON savings_goals(user_id);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);
```

### Caching Strategy
- Redis for session storage
- Application-level caching for frequent queries
- CDN for static assets

### Auto-scaling Configuration
```yaml
# HPA configuration
minReplicas: 3
maxReplicas: 10
targetCPUUtilizationPercentage: 70
targetMemoryUtilizationPercentage: 80
```

## Troubleshooting

### Common Issues

#### 1. Pod Startup Failures
```bash
# Check pod events
kubectl describe pod <pod-name> -n bloom-budget

# Check logs
kubectl logs <pod-name> -n bloom-budget

# Common fixes
kubectl delete pod <pod-name> -n bloom-budget  # Restart pod
```

#### 2. Database Connection Issues
```bash
# Test database connectivity
kubectl exec -it deployment/mysql-deployment -n bloom-budget -- \
  mysql -u bloom_budget_user -p -e "SHOW DATABASES;"

# Check database service
kubectl get service mysql-service -n bloom-budget
```

#### 3. High Memory Usage
```bash
# Check resource usage
kubectl top pods -n bloom-budget

# Scale up if needed
kubectl scale deployment backend-deployment --replicas=5 -n bloom-budget
```

### Health Checks

#### Application Health
```bash
# API health check
curl -f https://api.bloombudget.alinma.com/health

# Database health
kubectl exec -it deployment/backend-deployment -n bloom-budget -- \
  python -c "from app.core.database import engine; print(engine.execute('SELECT 1').scalar())"
```

#### Infrastructure Health
```bash
# Check all pods
kubectl get pods -n bloom-budget

# Check services
kubectl get services -n bloom-budget

# Check ingress
kubectl get ingress -n bloom-budget
```

## Rollback Procedures

### Automatic Rollback
```bash
# Rollback to previous version
./scripts/deploy.sh -r production

# Rollback specific deployment
kubectl rollout undo deployment/backend-deployment -n bloom-budget
```

### Manual Rollback
```bash
# List deployment history
kubectl rollout history deployment/backend-deployment -n bloom-budget

# Rollback to specific revision
kubectl rollout undo deployment/backend-deployment --to-revision=2 -n bloom-budget
```

## Maintenance

### Regular Tasks
- **Daily**: Monitor application metrics and logs
- **Weekly**: Review security alerts and update dependencies
- **Monthly**: Performance optimization and capacity planning
- **Quarterly**: Disaster recovery testing

### Update Procedures
```bash
# Update application
git pull origin main
./scripts/deploy.sh production

# Update Kubernetes cluster
kubectl apply -f k8s/

# Update monitoring stack
helm upgrade prometheus prometheus-community/kube-prometheus-stack
```

## Disaster Recovery

### Backup Strategy
- **Database**: Daily automated backups with 30-day retention
- **Files**: Daily backup of uploaded files
- **Configuration**: Version-controlled infrastructure as code

### Recovery Procedures
1. Restore database from latest backup
2. Restore application files
3. Redeploy application
4. Verify functionality
5. Update DNS if needed

### RTO/RPO Targets
- **Recovery Time Objective (RTO)**: 4 hours
- **Recovery Point Objective (RPO)**: 1 hour

## Support and Contacts

### Emergency Contacts
- **DevOps Team**: <EMAIL>
- **Development Team**: <EMAIL>
- **Security Team**: <EMAIL>

### Escalation Procedures
1. **Level 1**: Application issues → Development Team
2. **Level 2**: Infrastructure issues → DevOps Team
3. **Level 3**: Security incidents → Security Team

### Documentation
- **API Documentation**: https://api.bloombudget.alinma.com/docs
- **Monitoring**: https://monitoring.bloombudget.alinma.com
- **Status Page**: https://status.bloombudget.alinma.com
