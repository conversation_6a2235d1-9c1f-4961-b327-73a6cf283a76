from sqlalchemy import Column, Integer, String, DateTime, <PERSON>olean, Float, Text, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..core.database import Base
import enum

class MessageType(enum.Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"

class ChatSessionStatus(enum.Enum):
    ACTIVE = "active"
    COMPLETED = "completed"
    ARCHIVED = "archived"

class ChatSession(Base):
    __tablename__ = "chat_sessions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Session details
    session_title = Column(String(200))
    session_type = Column(String(50), default="financial_advice")  # financial_advice, goal_planning, etc.
    status = Column(Enum(ChatSessionStatus), default=ChatSessionStatus.ACTIVE)
    
    # Context and state
    context_data = Column(Text)  # JSON string with session context
    current_topic = Column(String(100))
    
    # Session metrics
    message_count = Column(Integer, default=0)
    total_tokens_used = Column(Integer, default=0)
    
    # Timestamps
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    last_activity = Column(DateTime(timezone=True), server_default=func.now())
    ended_at = Column(DateTime(timezone=True))
    
    # Relationships
    user = relationship("User", back_populates="chat_sessions")
    messages = relationship("ChatMessage", back_populates="session", order_by="ChatMessage.created_at")
    
    @property
    def duration_minutes(self):
        """Calculate session duration in minutes"""
        if self.ended_at:
            duration = self.ended_at - self.started_at
        else:
            from datetime import datetime
            duration = datetime.now(self.started_at.tzinfo) - self.started_at
        return int(duration.total_seconds() / 60)

    def __repr__(self):
        return f"<ChatSession(id={self.id}, user_id={self.user_id}, type='{self.session_type}')>"

class ChatMessage(Base):
    __tablename__ = "chat_messages"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("chat_sessions.id"), nullable=False)
    
    # Message details
    message_type = Column(Enum(MessageType), nullable=False)
    content = Column(Text, nullable=False)
    
    # AI-specific fields
    ai_model = Column(String(50))  # e.g., "gpt-3.5-turbo"
    tokens_used = Column(Integer)
    response_time_ms = Column(Integer)
    
    # Message metadata
    is_edited = Column(Boolean, default=False)
    edited_at = Column(DateTime(timezone=True))
    
    # Feedback
    user_rating = Column(Integer)  # 1-5 rating for AI responses
    user_feedback = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    session = relationship("ChatSession", back_populates="messages")

    def __repr__(self):
        return f"<ChatMessage(id={self.id}, type='{self.message_type}', session_id={self.session_id})>"

class FinancialAdvice(Base):
    __tablename__ = "financial_advice"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Advice details
    advice_type = Column(String(50), nullable=False)  # savings_plan, budget_optimization, etc.
    title = Column(String(200), nullable=False)
    content = Column(Text, nullable=False)
    
    # Personalization
    user_context = Column(Text)  # JSON with user's financial context
    priority_level = Column(Integer, default=1)  # 1-5 priority
    
    # Implementation tracking
    is_implemented = Column(Boolean, default=False)
    implementation_date = Column(DateTime(timezone=True))
    effectiveness_rating = Column(Integer)  # User's rating of advice effectiveness
    
    # AI generation metadata
    ai_model_used = Column(String(50))
    confidence_score = Column(Float)  # AI's confidence in the advice
    
    # Timestamps
    generated_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True))  # When advice becomes outdated
    
    # Relationships
    user = relationship("User")

    def __repr__(self):
        return f"<FinancialAdvice(id={self.id}, type='{self.advice_type}', user_id={self.user_id})>"
