from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, Text, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..core.database import Base
import enum

class ChallengeStatus(enum.Enum):
    PENDING = "pending"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class ChallengeType(enum.Enum):
    SAVINGS_AMOUNT = "savings_amount"
    SAVINGS_FREQUENCY = "savings_frequency"
    GOAL_COMPLETION = "goal_completion"
    ROSE_COLLECTION = "rose_collection"

class Challenge(Base):
    __tablename__ = "challenges"

    id = Column(Integer, primary_key=True, index=True)
    creator_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Challenge details
    title = Column(String(100), nullable=False)
    description = Column(Text)
    challenge_type = Column(Enum(ChallengeType), nullable=False)
    
    # Challenge parameters
    target_amount = Column(Float)
    target_frequency = Column(Integer)  # For frequency-based challenges
    duration_days = Column(Integer, default=30)
    
    # Status and timeline
    status = Column(Enum(ChallengeStatus), default=ChallengeStatus.PENDING)
    start_date = Column(DateTime(timezone=True))
    end_date = Column(DateTime(timezone=True))
    
    # Participation settings
    max_participants = Column(Integer, default=10)
    is_public = Column(Boolean, default=False)
    invite_code = Column(String(20), unique=True)
    
    # Rewards and incentives
    reward_description = Column(Text)
    winner_reward_amount = Column(Float)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    creator = relationship("User", foreign_keys=[creator_id], back_populates="challenges_created")
    participants = relationship("ChallengeParticipant", back_populates="challenge")
    
    @property
    def participant_count(self):
        return len(self.participants)
    
    @property
    def is_full(self):
        return self.participant_count >= self.max_participants
    
    @property
    def days_remaining(self):
        if self.end_date:
            from datetime import datetime
            remaining = self.end_date - datetime.now(self.end_date.tzinfo)
            return max(remaining.days, 0)
        return 0

    def __repr__(self):
        return f"<Challenge(id={self.id}, title='{self.title}', type='{self.challenge_type}')>"

class ChallengeParticipant(Base):
    __tablename__ = "challenge_participants"

    id = Column(Integer, primary_key=True, index=True)
    challenge_id = Column(Integer, ForeignKey("challenges.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Participation details
    joined_at = Column(DateTime(timezone=True), server_default=func.now())
    is_active = Column(Boolean, default=True)
    
    # Progress tracking
    current_progress = Column(Float, default=0.0)
    progress_percentage = Column(Float, default=0.0)
    rank = Column(Integer)
    
    # Achievement
    is_winner = Column(Boolean, default=False)
    completion_date = Column(DateTime(timezone=True))
    final_score = Column(Float)
    
    # Timestamps
    last_activity = Column(DateTime(timezone=True))
    
    # Relationships
    challenge = relationship("Challenge", back_populates="participants")
    user = relationship("User", back_populates="challenge_participations")
    progress_updates = relationship("ChallengeProgress", back_populates="participant")

    def __repr__(self):
        return f"<ChallengeParticipant(id={self.id}, challenge_id={self.challenge_id}, user_id={self.user_id})>"

class ChallengeProgress(Base):
    __tablename__ = "challenge_progress"

    id = Column(Integer, primary_key=True, index=True)
    participant_id = Column(Integer, ForeignKey("challenge_participants.id"), nullable=False)
    
    # Progress details
    progress_amount = Column(Float, nullable=False)
    progress_description = Column(Text)
    
    # Verification
    is_verified = Column(Boolean, default=True)
    verified_by = Column(String(100))
    
    # Timestamps
    recorded_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    participant = relationship("ChallengeParticipant", back_populates="progress_updates")

    def __repr__(self):
        return f"<ChallengeProgress(id={self.id}, participant_id={self.participant_id}, amount={self.progress_amount})>"
