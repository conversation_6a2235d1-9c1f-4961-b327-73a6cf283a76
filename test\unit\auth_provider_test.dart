import 'package:flutter_test/flutter_test.dart';
import 'package:bloom_budget_app/features/auth/presentation/providers/auth_provider.dart';
import 'package:bloom_budget_app/core/storage/local_storage.dart';

void main() {
  group('AuthProvider Tests', () {
    late AuthProvider authProvider;

    setUp(() {
      authProvider = AuthProvider();
    });

    test('should initialize with correct default values', () {
      expect(authProvider.isLoading, false);
      expect(authProvider.isAuthenticated, false);
      expect(authProvider.token, null);
      expect(authProvider.userData, null);
      expect(authProvider.errorMessage, null);
    });

    test('should set loading state correctly during login', () async {
      // Start login process
      final loginFuture = authProvider.login('testuser', 'password123');
      
      // Check loading state is true
      expect(authProvider.isLoading, true);
      
      // Wait for completion
      await loginFuture;
      
      // Check loading state is false
      expect(authProvider.isLoading, false);
    });

    test('should login successfully with valid credentials', () async {
      final result = await authProvider.login('testuser', 'password123');
      
      expect(result, true);
      expect(authProvider.isAuthenticated, true);
      expect(authProvider.token, isNotNull);
      expect(authProvider.userData, isNotNull);
      expect(authProvider.errorMessage, null);
    });

    test('should fail login with empty credentials', () async {
      final result = await authProvider.login('', '');
      
      expect(result, false);
      expect(authProvider.isAuthenticated, false);
      expect(authProvider.token, null);
      expect(authProvider.errorMessage, isNotNull);
    });

    test('should register successfully with valid data', () async {
      final userData = {
        'username': 'newuser',
        'email': '<EMAIL>',
        'full_name': 'New User',
        'phone_number': '+966501234567',
      };
      
      final result = await authProvider.register(userData);
      
      expect(result, true);
      expect(authProvider.isAuthenticated, true);
      expect(authProvider.token, isNotNull);
      expect(authProvider.userData, isNotNull);
      expect(authProvider.userData!['username'], 'newuser');
    });

    test('should logout successfully', () async {
      // First login
      await authProvider.login('testuser', 'password123');
      expect(authProvider.isAuthenticated, true);
      
      // Then logout
      await authProvider.logout();
      
      expect(authProvider.isAuthenticated, false);
      expect(authProvider.token, null);
      expect(authProvider.userData, null);
    });

    test('should clear error message', () {
      // Set an error
      authProvider.login('', '');
      expect(authProvider.errorMessage, isNotNull);
      
      // Clear error
      authProvider.clearError();
      expect(authProvider.errorMessage, null);
    });
  });
}
