import pytest
import asyncio
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.main import app
from app.core.database import get_db, Base
from app.models.user import User

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture(scope="module")
def client():
    Base.metadata.create_all(bind=engine)
    with TestClient(app) as c:
        yield c
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def test_user_data():
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "full_name": "Test User",
        "phone_number": "+966501234567",
        "password": "testpassword123",
        "preferred_language": "ar"
    }

class TestAuthentication:
    def test_register_user_success(self, client, test_user_data):
        """Test successful user registration"""
        response = client.post("/api/v1/auth/register", json=test_user_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["username"] == test_user_data["username"]
        assert data["email"] == test_user_data["email"]
        assert data["full_name"] == test_user_data["full_name"]
        assert "id" in data
        assert data["is_active"] == True
        assert data["total_savings"] == 0.0

    def test_register_duplicate_email(self, client, test_user_data):
        """Test registration with duplicate email"""
        # First registration
        client.post("/api/v1/auth/register", json=test_user_data)
        
        # Second registration with same email
        duplicate_data = test_user_data.copy()
        duplicate_data["username"] = "differentuser"
        response = client.post("/api/v1/auth/register", json=duplicate_data)
        
        assert response.status_code == 400
        assert "Email already registered" in response.json()["detail"]

    def test_register_duplicate_username(self, client, test_user_data):
        """Test registration with duplicate username"""
        # First registration
        client.post("/api/v1/auth/register", json=test_user_data)
        
        # Second registration with same username
        duplicate_data = test_user_data.copy()
        duplicate_data["email"] = "<EMAIL>"
        response = client.post("/api/v1/auth/register", json=duplicate_data)
        
        assert response.status_code == 400
        assert "Username already taken" in response.json()["detail"]

    def test_register_invalid_data(self, client):
        """Test registration with invalid data"""
        invalid_data = {
            "username": "",  # Empty username
            "email": "invalid-email",  # Invalid email format
            "password": "123",  # Too short password
        }
        
        response = client.post("/api/v1/auth/register", json=invalid_data)
        assert response.status_code == 422  # Validation error

    def test_login_success(self, client, test_user_data):
        """Test successful login"""
        # First register a user
        client.post("/api/v1/auth/register", json=test_user_data)
        
        # Then login
        login_data = {
            "username": test_user_data["username"],
            "password": test_user_data["password"]
        }
        response = client.post("/api/v1/auth/login", data=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"

    def test_login_with_email(self, client, test_user_data):
        """Test login with email instead of username"""
        # First register a user
        client.post("/api/v1/auth/register", json=test_user_data)
        
        # Then login with email
        login_data = {
            "username": test_user_data["email"],
            "password": test_user_data["password"]
        }
        response = client.post("/api/v1/auth/login", data=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data

    def test_login_invalid_credentials(self, client, test_user_data):
        """Test login with invalid credentials"""
        # Register a user first
        client.post("/api/v1/auth/register", json=test_user_data)
        
        # Try to login with wrong password
        login_data = {
            "username": test_user_data["username"],
            "password": "wrongpassword"
        }
        response = client.post("/api/v1/auth/login", data=login_data)
        
        assert response.status_code == 401
        assert "Incorrect username or password" in response.json()["detail"]

    def test_login_nonexistent_user(self, client):
        """Test login with non-existent user"""
        login_data = {
            "username": "nonexistent",
            "password": "password123"
        }
        response = client.post("/api/v1/auth/login", data=login_data)
        
        assert response.status_code == 401

    def test_get_current_user_success(self, client, test_user_data):
        """Test getting current user info with valid token"""
        # Register and login
        client.post("/api/v1/auth/register", json=test_user_data)
        login_response = client.post("/api/v1/auth/login", data={
            "username": test_user_data["username"],
            "password": test_user_data["password"]
        })
        token = login_response.json()["access_token"]
        
        # Get current user
        headers = {"Authorization": f"Bearer {token}"}
        response = client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["username"] == test_user_data["username"]
        assert data["email"] == test_user_data["email"]

    def test_get_current_user_invalid_token(self, client):
        """Test getting current user with invalid token"""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == 401

    def test_get_current_user_no_token(self, client):
        """Test getting current user without token"""
        response = client.get("/api/v1/auth/me")
        
        assert response.status_code == 401

    def test_logout_success(self, client, test_user_data):
        """Test successful logout"""
        # Register and login
        client.post("/api/v1/auth/register", json=test_user_data)
        login_response = client.post("/api/v1/auth/login", data={
            "username": test_user_data["username"],
            "password": test_user_data["password"]
        })
        token = login_response.json()["access_token"]
        
        # Logout
        headers = {"Authorization": f"Bearer {token}"}
        response = client.post("/api/v1/auth/logout", headers=headers)
        
        assert response.status_code == 200
        assert "Successfully logged out" in response.json()["message"]

    def test_password_hashing(self, client, test_user_data):
        """Test that passwords are properly hashed"""
        response = client.post("/api/v1/auth/register", json=test_user_data)
        
        # Check that password is not stored in plain text
        db = TestingSessionLocal()
        user = db.query(User).filter(User.username == test_user_data["username"]).first()
        assert user.hashed_password != test_user_data["password"]
        assert len(user.hashed_password) > 50  # Hashed passwords are much longer
        db.close()

    def test_token_expiration(self, client, test_user_data):
        """Test token expiration handling"""
        # This would require mocking time or using a very short expiration
        # For now, we'll just test that tokens have expiration claims
        client.post("/api/v1/auth/register", json=test_user_data)
        login_response = client.post("/api/v1/auth/login", data={
            "username": test_user_data["username"],
            "password": test_user_data["password"]
        })
        
        token = login_response.json()["access_token"]
        assert token is not None
        assert len(token) > 50  # JWT tokens are long strings

    def test_user_activation_status(self, client, test_user_data):
        """Test user activation status"""
        response = client.post("/api/v1/auth/register", json=test_user_data)
        
        # New users should be active by default
        assert response.json()["is_active"] == True
        
        # Test login with inactive user would require additional setup
        # This is a placeholder for more complex activation logic
