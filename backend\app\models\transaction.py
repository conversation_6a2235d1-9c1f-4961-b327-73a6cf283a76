from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, Text, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..core.database import Base
import enum

class TransactionType(enum.Enum):
    DEPOSIT = "deposit"
    WITHDRAWAL = "withdrawal"
    TRANSFER = "transfer"
    SAFE_LOCK = "safe_lock"
    SAFE_UNLOCK = "safe_unlock"
    GOAL_CONTRIBUTION = "goal_contribution"
    CHALLENGE_REWARD = "challenge_reward"

class TransactionStatus(enum.Enum):
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class Transaction(Base):
    __tablename__ = "transactions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    savings_goal_id = Column(Integer, ForeignKey("savings_goals.id"))
    
    # Transaction details
    transaction_type = Column(Enum(TransactionType), nullable=False)
    amount = Column(Float, nullable=False)
    currency = Column(String(3), default="SAR")
    
    # Status and processing
    status = Column(Enum(TransactionStatus), default=TransactionStatus.PENDING)
    reference_number = Column(String(50), unique=True, nullable=False)
    
    # Description and metadata
    description = Column(Text)
    category = Column(String(50))
    tags = Column(String(200))  # Comma-separated tags
    
    # Related entities
    digital_safe_id = Column(Integer, ForeignKey("digital_safes.id"))
    challenge_id = Column(Integer, ForeignKey("challenges.id"))
    
    # Banking integration
    external_transaction_id = Column(String(100))
    bank_reference = Column(String(100))
    
    # Balance tracking
    balance_before = Column(Float)
    balance_after = Column(Float)
    
    # Processing details
    processed_at = Column(DateTime(timezone=True))
    processing_fee = Column(Float, default=0.0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="transactions")
    savings_goal = relationship("SavingsGoal", back_populates="transactions")
    digital_safe = relationship("DigitalSafe")
    challenge = relationship("Challenge")

    def __repr__(self):
        return f"<Transaction(id={self.id}, type='{self.transaction_type}', amount={self.amount})>"

class TransactionLog(Base):
    __tablename__ = "transaction_logs"

    id = Column(Integer, primary_key=True, index=True)
    transaction_id = Column(Integer, ForeignKey("transactions.id"), nullable=False)
    
    # Log details
    action = Column(String(50), nullable=False)  # created, updated, processed, failed, etc.
    previous_status = Column(String(20))
    new_status = Column(String(20))
    
    # Additional information
    details = Column(Text)  # JSON with additional details
    error_message = Column(Text)
    
    # System information
    ip_address = Column(String(45))
    user_agent = Column(String(500))
    
    # Timestamps
    logged_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    transaction = relationship("Transaction")

    def __repr__(self):
        return f"<TransactionLog(id={self.id}, action='{self.action}', transaction_id={self.transaction_id})>"
