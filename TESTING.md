# BloomBudget Testing Guide

## Overview
This document provides comprehensive testing guidelines for the BloomBudget application, covering both Flutter frontend and FastAPI backend components.

## Test Structure

### Frontend Tests (Flutter)
```
test/
├── unit/                 # Unit tests for business logic
│   ├── auth_provider_test.dart
│   ├── home_provider_test.dart
│   └── ...
├── widget/              # Widget tests for UI components
│   ├── login_page_test.dart
│   ├── dashboard_card_test.dart
│   └── ...
└── integration/         # Integration tests for complete flows
    ├── app_flow_test.dart
    └── ...
```

### Backend Tests (Python)
```
backend/tests/
├── test_auth.py         # Authentication API tests
├── test_savings_goals.py # Savings goals API tests
├── test_rose_garden.py  # Rose garden API tests
├── test_digital_safe.py # Digital safe API tests
└── ...
```

## Running Tests

### Quick Start
```bash
# Run all tests
python test_runner.py

# Run only Flutter tests
python test_runner.py --platform flutter

# Run only backend tests
python test_runner.py --platform backend

# Run specific test types
python test_runner.py --type unit
python test_runner.py --type integration
python test_runner.py --type security
```

### Manual Test Execution

#### Flutter Tests
```bash
# Unit tests
flutter test test/unit/

# Widget tests
flutter test test/widget/

# Integration tests
flutter test integration_test/

# All tests with coverage
flutter test --coverage
```

#### Backend Tests
```bash
cd backend
pip install pytest pytest-asyncio
python -m pytest tests/ -v
python -m pytest tests/ --cov=app --cov-report=html
```

## Test Categories

### 1. Unit Tests
**Purpose**: Test individual functions and classes in isolation

**Flutter Examples**:
- AuthProvider login/logout logic
- HomeProvider state management
- Utility functions and calculations

**Backend Examples**:
- Password hashing functions
- JWT token generation
- Business logic calculations

### 2. Widget Tests
**Purpose**: Test UI components and their interactions

**Coverage**:
- Form validation
- Button interactions
- State changes in UI
- Navigation flows
- Responsive design

### 3. Integration Tests
**Purpose**: Test complete user flows and system interactions

**Scenarios**:
- Complete login flow
- Savings goal creation and contribution
- Rose garden updates
- Digital safe operations
- Social challenge participation

### 4. API Tests
**Purpose**: Test backend endpoints and data flow

**Coverage**:
- Authentication endpoints
- CRUD operations for all entities
- Error handling
- Data validation
- Security measures

### 5. Security Tests
**Purpose**: Ensure application security

**Checks**:
- Dependency vulnerabilities
- Authentication bypass attempts
- Data encryption verification
- Input sanitization
- SQL injection prevention

### 6. Performance Tests
**Purpose**: Verify application performance

**Metrics**:
- App startup time
- API response times
- Memory usage
- Battery consumption
- Network efficiency

## Test Data Management

### Test Database
- Uses SQLite for backend tests
- Isolated test environment
- Automatic cleanup after tests

### Mock Data
- Consistent test user data
- Realistic savings goals
- Sample transactions
- Mock API responses

## Continuous Integration

### GitHub Actions Workflow
```yaml
name: BloomBudget Tests
on: [push, pull_request]
jobs:
  flutter-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
      - run: flutter test
  
  backend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-python@v2
      - run: pip install -r backend/requirements.txt
      - run: cd backend && python -m pytest
```

## Test Coverage Goals

### Minimum Coverage Requirements
- **Unit Tests**: 90%+ coverage
- **Widget Tests**: 80%+ coverage
- **API Tests**: 95%+ coverage
- **Integration Tests**: Key user flows covered

### Coverage Reporting
```bash
# Flutter coverage
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html

# Backend coverage
cd backend
python -m pytest --cov=app --cov-report=html
```

## Testing Best Practices

### 1. Test Naming
- Use descriptive test names
- Follow pattern: `should_[expected_behavior]_when_[condition]`
- Group related tests using `group()` or classes

### 2. Test Structure
- **Arrange**: Set up test data and conditions
- **Act**: Execute the code being tested
- **Assert**: Verify the expected outcome

### 3. Mock Usage
- Mock external dependencies
- Use consistent mock data
- Avoid over-mocking

### 4. Test Isolation
- Each test should be independent
- Clean up after tests
- Use fresh test data

## Device Testing

### Physical Device Testing
- Test on various Android devices
- Test on different iOS devices
- Verify different screen sizes
- Test different OS versions

### Emulator/Simulator Testing
- Android emulators for different API levels
- iOS simulators for different versions
- Tablet and phone form factors

## Accessibility Testing

### Flutter Accessibility
```dart
testWidgets('should be accessible', (WidgetTester tester) async {
  await tester.pumpWidget(MyWidget());
  
  // Test semantic labels
  expect(find.bySemanticsLabel('Login button'), findsOneWidget);
  
  // Test focus traversal
  await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
    'flutter/accessibility',
    // ... accessibility test data
  );
});
```

### Accessibility Checklist
- [ ] All interactive elements have semantic labels
- [ ] Proper focus order
- [ ] Sufficient color contrast
- [ ] Screen reader compatibility
- [ ] Keyboard navigation support

## Performance Testing

### Flutter Performance
```dart
testWidgets('should perform well', (WidgetTester tester) async {
  await tester.pumpWidget(MyApp());
  
  // Measure frame rendering time
  await tester.binding.reassembleApplication();
  
  // Test scroll performance
  await tester.drag(find.byType(ListView), Offset(0, -300));
  await tester.pumpAndSettle();
});
```

### Backend Performance
```python
def test_api_response_time():
    start_time = time.time()
    response = client.get("/api/v1/savings-goals/")
    end_time = time.time()
    
    assert response.status_code == 200
    assert (end_time - start_time) < 0.5  # Less than 500ms
```

## Troubleshooting

### Common Issues
1. **Test Database Conflicts**: Ensure proper cleanup
2. **Async Test Failures**: Use proper async/await patterns
3. **Widget Test Timeouts**: Increase timeout for complex widgets
4. **Mock Data Inconsistencies**: Use factories for test data

### Debug Commands
```bash
# Flutter test debugging
flutter test --verbose
flutter test --debug

# Backend test debugging
python -m pytest -v -s
python -m pytest --pdb  # Drop into debugger on failure
```

## Test Maintenance

### Regular Tasks
- Update test data when models change
- Review and update mock responses
- Maintain test coverage above thresholds
- Update integration tests for new features

### Test Review Process
1. All new features must include tests
2. Test coverage must not decrease
3. Integration tests for critical paths
4. Performance tests for new heavy operations

## Reporting

### Test Reports
- Automated test reports in CI/CD
- Coverage reports generated after each run
- Performance benchmarks tracked over time
- Security scan results documented

### Metrics Tracking
- Test execution time trends
- Coverage percentage over time
- Failure rate analysis
- Performance regression detection
