from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db

router = APIRouter()

@router.get("/")
async def get_challenges(db: Session = Depends(get_db)):
    """Get all challenges"""
    return {"message": "Challenges endpoint - coming soon"}

@router.post("/")
async def create_challenge(db: Session = Depends(get_db)):
    """Create a new challenge"""
    return {"message": "Create challenge endpoint - coming soon"}

@router.get("/{challenge_id}")
async def get_challenge(challenge_id: int, db: Session = Depends(get_db)):
    """Get a specific challenge"""
    return {"message": f"Challenge {challenge_id} endpoint - coming soon"}
