from sqlalchemy import Column, Integer, String, DateTime, <PERSON><PERSON>an, Float, Text, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..core.database import Base
import enum

class SafeStatus(enum.Enum):
    LOCKED = "locked"
    UNLOCKED = "unlocked"
    EMERGENCY_UNLOCKED = "emergency_unlocked"

class GameType(enum.Enum):
    MATH_PUZZLE = "math_puzzle"
    MEMORY_GAME = "memory_game"
    PATTERN_MATCH = "pattern_match"
    TRIVIA_QUESTION = "trivia_question"

class DigitalSafe(Base):
    __tablename__ = "digital_safes"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Safe details
    safe_name = Column(String(100), nullable=False)
    locked_amount = Column(Float, nullable=False)
    lock_duration_days = Column(Integer, default=30)
    
    # Lock mechanism
    status = Column(Enum(SafeStatus), default=SafeStatus.LOCKED)
    game_type = Column(Enum(GameType), nullable=False)
    unlock_attempts = Column(Integer, default=0)
    max_attempts = Column(Integer, default=3)
    
    # Game configuration
    game_difficulty = Column(Integer, default=1)  # 1-5 difficulty level
    game_data = Column(Text)  # JSON string with game-specific data
    
    # Timeline
    locked_at = Column(DateTime(timezone=True), server_default=func.now())
    unlock_available_at = Column(DateTime(timezone=True))
    unlocked_at = Column(DateTime(timezone=True))
    
    # Emergency unlock
    emergency_unlock_requested = Column(Boolean, default=False)
    emergency_unlock_reason = Column(Text)
    emergency_unlock_approved = Column(Boolean, default=False)
    emergency_unlock_approved_by = Column(String(100))
    emergency_unlock_approved_at = Column(DateTime(timezone=True))
    
    # Visual customization
    safe_color = Column(String(7), default="#FFD700")  # Gold color
    safe_icon = Column(String(50), default="default")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="digital_safes")
    unlock_attempts_history = relationship("SafeUnlockAttempt", back_populates="safe")
    
    @property
    def is_unlockable(self):
        """Check if safe can be unlocked based on time and attempts"""
        from datetime import datetime
        if self.status != SafeStatus.LOCKED:
            return False
        if self.unlock_available_at and datetime.now() < self.unlock_available_at:
            return False
        return self.unlock_attempts < self.max_attempts
    
    @property
    def days_until_unlock(self):
        """Calculate days remaining until safe can be unlocked"""
        if not self.unlock_available_at:
            return 0
        from datetime import datetime
        remaining = self.unlock_available_at - datetime.now()
        return max(remaining.days, 0)

    def __repr__(self):
        return f"<DigitalSafe(id={self.id}, name='{self.safe_name}', amount={self.locked_amount})>"

class SafeUnlockAttempt(Base):
    __tablename__ = "safe_unlock_attempts"

    id = Column(Integer, primary_key=True, index=True)
    safe_id = Column(Integer, ForeignKey("digital_safes.id"), nullable=False)
    
    # Attempt details
    attempt_number = Column(Integer, nullable=False)
    game_type = Column(Enum(GameType), nullable=False)
    was_successful = Column(Boolean, default=False)
    
    # Game results
    score = Column(Integer)
    time_taken_seconds = Column(Integer)
    game_result_data = Column(Text)  # JSON with detailed results
    
    # Timestamps
    attempted_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    safe = relationship("DigitalSafe", back_populates="unlock_attempts_history")

    def __repr__(self):
        return f"<SafeUnlockAttempt(id={self.id}, safe_id={self.safe_id}, successful={self.was_successful})>"
