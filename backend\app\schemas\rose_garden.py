from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from datetime import datetime

class RoseBase(BaseModel):
    rose_type: str
    savings_amount: float
    position_x: Optional[float] = 0.0
    position_y: Optional[float] = 0.0
    color: Optional[str] = None
    size: Optional[str] = "medium"
    bloom_stage: Optional[int] = 1

class RoseResponse(RoseBase):
    id: int
    garden_id: int
    created_at: datetime
    bloomed_at: datetime
    
    class Config:
        from_attributes = True

class RoseGardenBase(BaseModel):
    garden_name: str
    background_theme: Optional[str] = "default"
    garden_layout: Optional[Dict[str, Any]] = None
    is_public: Optional[bool] = False

class RoseGardenUpdate(BaseModel):
    garden_name: Optional[str] = None
    background_theme: Optional[str] = None
    garden_layout: Optional[Dict[str, Any]] = None
    is_public: Optional[bool] = None

class RoseGardenResponse(RoseGardenBase):
    id: int
    user_id: int
    total_roses: int
    garden_level: int
    basic_roses: int
    premium_roses: int
    luxury_roses: int
    share_count: int
    likes_count: int
    total_savings_contributed: float
    last_rose_added: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    garden_completion_percentage: float
    roses: Optional[List[RoseResponse]] = []
    
    class Config:
        from_attributes = True
