# BloomBudget Makefile
# Provides convenient commands for development and deployment

.PHONY: help install test build deploy clean

# Default target
.DEFAULT_GOAL := help

# Variables
ENVIRONMENT ?= staging
DOCKER_REGISTRY ?= ghcr.io/alinma
IMAGE_NAME ?= bloom-budget
VERSION ?= $(shell git rev-parse --short HEAD)

# Colors for output
BLUE := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
NC := \033[0m # No Color

## Help
help: ## Show this help message
	@echo "$(BLUE)BloomBudget Development Commands$(NC)"
	@echo ""
	@awk 'BEGIN {FS = ":.*##"; printf "Usage:\n  make $(GREEN)<target>$(NC)\n\nTargets:\n"} /^[a-zA-Z_-]+:.*?##/ { printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2 }' $(MAKEFILE_LIST)

## Development
install: ## Install dependencies for both frontend and backend
	@echo "$(BLUE)Installing Flutter dependencies...$(NC)"
	flutter pub get
	@echo "$(BLUE)Installing Python dependencies...$(NC)"
	cd backend && pip install -r requirements.txt
	@echo "$(GREEN)Dependencies installed successfully!$(NC)"

dev-setup: ## Set up development environment
	@echo "$(BLUE)Setting up development environment...$(NC)"
	cp backend/.env.example backend/.env
	docker-compose up -d mysql redis
	sleep 10
	cd backend && python -m alembic upgrade head
	@echo "$(GREEN)Development environment ready!$(NC)"

dev-start: ## Start development services
	@echo "$(BLUE)Starting development services...$(NC)"
	docker-compose up -d
	@echo "$(GREEN)Services started! API available at http://localhost:8000$(NC)"

dev-stop: ## Stop development services
	@echo "$(BLUE)Stopping development services...$(NC)"
	docker-compose down
	@echo "$(GREEN)Services stopped!$(NC)"

dev-logs: ## Show development logs
	docker-compose logs -f

## Testing
test: ## Run all tests
	@echo "$(BLUE)Running Flutter tests...$(NC)"
	flutter test --coverage
	@echo "$(BLUE)Running backend tests...$(NC)"
	cd backend && python -m pytest tests/ -v --cov=app
	@echo "$(GREEN)All tests completed!$(NC)"

test-flutter: ## Run Flutter tests only
	@echo "$(BLUE)Running Flutter tests...$(NC)"
	flutter test --coverage
	@echo "$(GREEN)Flutter tests completed!$(NC)"

test-backend: ## Run backend tests only
	@echo "$(BLUE)Running backend tests...$(NC)"
	cd backend && python -m pytest tests/ -v --cov=app
	@echo "$(GREEN)Backend tests completed!$(NC)"

test-integration: ## Run integration tests
	@echo "$(BLUE)Running integration tests...$(NC)"
	flutter test integration_test/
	@echo "$(GREEN)Integration tests completed!$(NC)"

test-performance: ## Run performance tests
	@echo "$(BLUE)Running performance tests...$(NC)"
	python test_runner.py --type performance
	@echo "$(GREEN)Performance tests completed!$(NC)"

## Code Quality
lint: ## Run linting for all code
	@echo "$(BLUE)Running Flutter analysis...$(NC)"
	flutter analyze
	@echo "$(BLUE)Running Python linting...$(NC)"
	cd backend && flake8 app/
	@echo "$(GREEN)Linting completed!$(NC)"

format: ## Format all code
	@echo "$(BLUE)Formatting Flutter code...$(NC)"
	flutter format .
	@echo "$(BLUE)Formatting Python code...$(NC)"
	cd backend && black app/
	@echo "$(GREEN)Code formatting completed!$(NC)"

security-scan: ## Run security scans
	@echo "$(BLUE)Running security scans...$(NC)"
	cd backend && safety check
	@echo "$(GREEN)Security scan completed!$(NC)"

## Building
build: ## Build all components
	@echo "$(BLUE)Building Flutter app...$(NC)"
	flutter build apk --release
	@echo "$(BLUE)Building Docker image...$(NC)"
	docker build -t $(DOCKER_REGISTRY)/$(IMAGE_NAME):$(VERSION) backend/
	docker tag $(DOCKER_REGISTRY)/$(IMAGE_NAME):$(VERSION) $(DOCKER_REGISTRY)/$(IMAGE_NAME):latest
	@echo "$(GREEN)Build completed!$(NC)"

build-flutter: ## Build Flutter app only
	@echo "$(BLUE)Building Flutter APK...$(NC)"
	flutter build apk --release
	@echo "$(GREEN)Flutter build completed!$(NC)"

build-docker: ## Build Docker image only
	@echo "$(BLUE)Building Docker image...$(NC)"
	docker build -t $(DOCKER_REGISTRY)/$(IMAGE_NAME):$(VERSION) backend/
	docker tag $(DOCKER_REGISTRY)/$(IMAGE_NAME):$(VERSION) $(DOCKER_REGISTRY)/$(IMAGE_NAME):latest
	@echo "$(GREEN)Docker build completed!$(NC)"

push: ## Push Docker image to registry
	@echo "$(BLUE)Pushing Docker image...$(NC)"
	docker push $(DOCKER_REGISTRY)/$(IMAGE_NAME):$(VERSION)
	docker push $(DOCKER_REGISTRY)/$(IMAGE_NAME):latest
	@echo "$(GREEN)Image pushed successfully!$(NC)"

## Deployment
deploy: ## Deploy to specified environment (default: staging)
	@echo "$(BLUE)Deploying to $(ENVIRONMENT)...$(NC)"
	./scripts/deploy.sh $(ENVIRONMENT)
	@echo "$(GREEN)Deployment completed!$(NC)"

deploy-staging: ## Deploy to staging environment
	@$(MAKE) deploy ENVIRONMENT=staging

deploy-production: ## Deploy to production environment
	@$(MAKE) deploy ENVIRONMENT=production

rollback: ## Rollback deployment in specified environment
	@echo "$(YELLOW)Rolling back $(ENVIRONMENT) deployment...$(NC)"
	./scripts/deploy.sh -r $(ENVIRONMENT)
	@echo "$(GREEN)Rollback completed!$(NC)"

## Database
db-migrate: ## Run database migrations
	@echo "$(BLUE)Running database migrations...$(NC)"
	cd backend && python -m alembic upgrade head
	@echo "$(GREEN)Migrations completed!$(NC)"

db-seed: ## Seed database with sample data
	@echo "$(BLUE)Seeding database...$(NC)"
	cd backend && python scripts/seed_data.py
	@echo "$(GREEN)Database seeded!$(NC)"

db-backup: ## Create database backup
	@echo "$(BLUE)Creating database backup...$(NC)"
	./scripts/backup.sh
	@echo "$(GREEN)Backup completed!$(NC)"

db-restore: ## Restore database from backup (requires BACKUP_TIMESTAMP)
	@echo "$(BLUE)Restoring database...$(NC)"
	@if [ -z "$(BACKUP_TIMESTAMP)" ]; then \
		echo "$(RED)Error: BACKUP_TIMESTAMP is required$(NC)"; \
		echo "Usage: make db-restore BACKUP_TIMESTAMP=20231201_143000"; \
		exit 1; \
	fi
	./scripts/restore.sh $(BACKUP_TIMESTAMP)
	@echo "$(GREEN)Restore completed!$(NC)"

## Monitoring
logs: ## Show application logs
	@echo "$(BLUE)Showing application logs...$(NC)"
	kubectl logs -f deployment/backend-deployment -n bloom-budget

monitor: ## Open monitoring dashboard
	@echo "$(BLUE)Opening monitoring dashboard...$(NC)"
	open http://localhost:3000

status: ## Check application status
	@echo "$(BLUE)Checking application status...$(NC)"
	@echo "API Health:"
	@curl -s http://localhost:8000/health | jq . || echo "API not available"
	@echo "\nKubernetes Pods:"
	@kubectl get pods -n bloom-budget 2>/dev/null || echo "Kubernetes not available"

## Maintenance
clean: ## Clean up development environment
	@echo "$(BLUE)Cleaning up...$(NC)"
	flutter clean
	docker-compose down -v
	docker system prune -f
	@echo "$(GREEN)Cleanup completed!$(NC)"

reset: ## Reset development environment
	@echo "$(YELLOW)Resetting development environment...$(NC)"
	@$(MAKE) clean
	@$(MAKE) dev-setup
	@echo "$(GREEN)Environment reset completed!$(NC)"

update: ## Update dependencies
	@echo "$(BLUE)Updating Flutter dependencies...$(NC)"
	flutter pub upgrade
	@echo "$(BLUE)Updating Python dependencies...$(NC)"
	cd backend && pip install -r requirements.txt --upgrade
	@echo "$(GREEN)Dependencies updated!$(NC)"

## Documentation
docs: ## Generate documentation
	@echo "$(BLUE)Generating API documentation...$(NC)"
	cd backend && python -c "import app.main; print('API docs available at http://localhost:8000/docs')"
	@echo "$(GREEN)Documentation ready!$(NC)"

## Utilities
shell: ## Open backend shell
	docker-compose exec backend bash

db-shell: ## Open database shell
	docker-compose exec mysql mysql -u bloom_budget_user -p bloom_budget_db

redis-shell: ## Open Redis shell
	docker-compose exec redis redis-cli

## CI/CD
ci-test: ## Run CI tests (used by GitHub Actions)
	@$(MAKE) test
	@$(MAKE) lint
	@$(MAKE) security-scan

ci-build: ## Build for CI (used by GitHub Actions)
	@$(MAKE) build

ci-deploy: ## Deploy from CI (used by GitHub Actions)
	@$(MAKE) deploy ENVIRONMENT=$(ENVIRONMENT)

## Quick Commands
quick-start: install dev-setup dev-start ## Quick start for new developers
	@echo "$(GREEN)🚀 BloomBudget is ready for development!$(NC)"
	@echo "$(BLUE)API:$(NC) http://localhost:8000"
	@echo "$(BLUE)Docs:$(NC) http://localhost:8000/docs"
	@echo "$(BLUE)Grafana:$(NC) http://localhost:3000"

quick-test: test lint ## Quick test and lint
	@echo "$(GREEN)✅ All checks passed!$(NC)"

quick-deploy: build push deploy ## Quick build and deploy
	@echo "$(GREEN)🚀 Deployment completed!$(NC)"
