from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db

router = APIRouter()

@router.get("/")
async def get_transactions(db: Session = Depends(get_db)):
    """Get all transactions"""
    return {"message": "Transactions endpoint - coming soon"}

@router.post("/")
async def create_transaction(db: Session = Depends(get_db)):
    """Create a new transaction"""
    return {"message": "Create transaction endpoint - coming soon"}

@router.get("/{transaction_id}")
async def get_transaction(transaction_id: int, db: Session = Depends(get_db)):
    """Get a specific transaction"""
    return {"message": f"Transaction {transaction_id} endpoint - coming soon"}
