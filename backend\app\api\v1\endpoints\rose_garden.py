from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from ....core.database import get_db
from ....models.user import User
from ....models.rose_garden import <PERSON><PERSON><PERSON><PERSON>, <PERSON>
from ....schemas.rose_garden import RoseGardenResponse, RoseResponse, RoseGardenUpdate
from .auth import get_current_user

router = APIRouter()

@router.get("/", response_model=RoseGardenResponse)
def get_rose_garden(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's rose garden"""
    garden = db.query(RoseGarden).filter(RoseGarden.user_id == current_user.id).first()
    
    if not garden:
        # Create a new garden for the user
        garden = RoseGarden(
            user_id=current_user.id,
            garden_name=f"{current_user.full_name}'s Rose Garden"
        )
        db.add(garden)
        db.commit()
        db.refresh(garden)
    
    return garden

@router.put("/", response_model=RoseGardenResponse)
def update_rose_garden(
    garden_update: RoseGardenUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update rose garden settings"""
    garden = db.query(RoseGarden).filter(RoseGarden.user_id == current_user.id).first()
    
    if not garden:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Rose garden not found"
        )
    
    # Update fields
    update_data = garden_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(garden, field, value)
    
    db.commit()
    db.refresh(garden)
    
    return garden

@router.post("/add-rose")
def add_rose_to_garden(
    savings_amount: float,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Add a rose to the garden based on savings amount"""
    if savings_amount <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Savings amount must be positive"
        )
    
    garden = db.query(RoseGarden).filter(RoseGarden.user_id == current_user.id).first()
    
    if not garden:
        # Create garden if it doesn't exist
        garden = RoseGarden(
            user_id=current_user.id,
            garden_name=f"{current_user.full_name}'s Rose Garden"
        )
        db.add(garden)
        db.flush()
    
    # Add rose to garden
    rose_type = garden.add_rose(savings_amount)
    
    # Create individual rose record
    rose = Rose(
        garden_id=garden.id,
        rose_type=rose_type,
        savings_amount=savings_amount,
        # Position will be calculated by frontend
        position_x=0.0,
        position_y=0.0
    )
    
    # Set rose color based on type
    if rose_type == "luxury":
        rose.color = "#8B0000"  # Dark red
        rose.size = "large"
    elif rose_type == "premium":
        rose.color = "#DC143C"  # Crimson
        rose.size = "medium"
    else:
        rose.color = "#FF69B4"  # Hot pink
        rose.size = "small"
    
    db.add(rose)
    db.commit()
    db.refresh(garden)
    
    return {
        "message": f"Added {rose_type} rose to your garden!",
        "rose_type": rose_type,
        "garden": garden,
        "new_level": garden.garden_level,
        "total_roses": garden.total_roses
    }

@router.get("/roses", response_model=List[RoseResponse])
def get_garden_roses(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all roses in the user's garden"""
    garden = db.query(RoseGarden).filter(RoseGarden.user_id == current_user.id).first()
    
    if not garden:
        return []
    
    roses = db.query(Rose).filter(Rose.garden_id == garden.id).order_by(Rose.created_at.desc()).all()
    return roses

@router.post("/share")
def share_garden(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Share rose garden (increment share count)"""
    garden = db.query(RoseGarden).filter(RoseGarden.user_id == current_user.id).first()
    
    if not garden:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Rose garden not found"
        )
    
    garden.share_count += 1
    garden.is_public = True
    db.commit()
    
    return {
        "message": "Garden shared successfully!",
        "share_count": garden.share_count
    }

@router.post("/like/{garden_id}")
def like_garden(
    garden_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Like another user's public garden"""
    garden = db.query(RoseGarden).filter(
        RoseGarden.id == garden_id,
        RoseGarden.is_public == True
    ).first()
    
    if not garden:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Public garden not found"
        )
    
    if garden.user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot like your own garden"
        )
    
    garden.likes_count += 1
    db.commit()
    
    return {
        "message": "Garden liked!",
        "likes_count": garden.likes_count
    }

@router.get("/public", response_model=List[RoseGardenResponse])
def get_public_gardens(
    limit: int = 10,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """Get public rose gardens"""
    gardens = db.query(RoseGarden).filter(
        RoseGarden.is_public == True
    ).order_by(
        RoseGarden.likes_count.desc(),
        RoseGarden.total_roses.desc()
    ).offset(offset).limit(limit).all()
    
    return gardens
