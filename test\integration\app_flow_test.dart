import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:bloom_budget_app/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('BloomBudget App Integration Tests', () {
    testWidgets('complete user flow from splash to home', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Should start with splash screen
      expect(find.text('BloomBudget'), findsOneWidget);
      expect(find.text('Smart Savings for Smart Youth'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Wait for splash screen to complete
      await tester.pumpAndSettle(const Duration(seconds: 4));

      // Should navigate to login page (assuming user is not logged in)
      expect(find.text('Username or Email'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
      expect(find.text('Login'), findsOneWidget);

      // Perform login
      await tester.enterText(find.byType(TextFormField).first, 'testuser');
      await tester.enterText(find.byType(TextFormField).last, 'password123');
      await tester.tap(find.text('Login'));
      await tester.pumpAndSettle();

      // Should navigate to home page
      expect(find.text('Welcome back!'), findsOneWidget);
      expect(find.text('Total Savings'), findsOneWidget);
      expect(find.text('Current Balance'), findsOneWidget);
      expect(find.text('Active Goals'), findsOneWidget);
      expect(find.text('Rose Garden'), findsOneWidget);
    });

    testWidgets('add savings flow', (WidgetTester tester) async {
      // Start the app and login (assuming we're already on home page)
      app.main();
      await tester.pumpAndSettle();
      
      // Skip splash and login for this test
      await tester.pumpAndSettle(const Duration(seconds: 4));
      
      // Login
      await tester.enterText(find.byType(TextFormField).first, 'testuser');
      await tester.enterText(find.byType(TextFormField).last, 'password123');
      await tester.tap(find.text('Login'));
      await tester.pumpAndSettle();

      // Tap the floating action button to add savings
      await tester.tap(find.byType(FloatingActionButton));
      await tester.pumpAndSettle();

      // Should show add savings dialog
      expect(find.text('Add Savings'), findsOneWidget);
      expect(find.text('Amount (SAR)'), findsOneWidget);

      // Enter amount
      await tester.enterText(find.byType(TextField), '500');
      await tester.tap(find.text('Add'));
      await tester.pumpAndSettle();

      // Dialog should close and savings should be updated
      expect(find.text('Add Savings'), findsNothing);
      
      // Check if transaction appears in recent activity
      expect(find.text('Savings deposit'), findsOneWidget);
      expect(find.text('+500 SAR'), findsOneWidget);
    });

    testWidgets('contribute to goal flow', (WidgetTester tester) async {
      // Start the app and navigate to home
      app.main();
      await tester.pumpAndSettle();
      
      // Skip splash and login
      await tester.pumpAndSettle(const Duration(seconds: 4));
      await tester.enterText(find.byType(TextFormField).first, 'testuser');
      await tester.enterText(find.byType(TextFormField).last, 'password123');
      await tester.tap(find.text('Login'));
      await tester.pumpAndSettle();

      // Find a savings goal card and tap the contribute button
      final contributeButton = find.byIcon(Icons.add_circle).first;
      await tester.tap(contributeButton);
      await tester.pumpAndSettle();

      // Should show contribute dialog
      expect(find.textContaining('Contribute to'), findsOneWidget);
      expect(find.text('Amount to contribute (SAR)'), findsOneWidget);

      // Enter contribution amount
      await tester.enterText(find.byType(TextField), '200');
      await tester.tap(find.text('Contribute'));
      await tester.pumpAndSettle();

      // Dialog should close and goal progress should update
      expect(find.textContaining('Contribute to'), findsNothing);
    });

    testWidgets('navigation between screens', (WidgetTester tester) async {
      // Start the app and navigate to home
      app.main();
      await tester.pumpAndSettle();
      
      // Skip splash and login
      await tester.pumpAndSettle(const Duration(seconds: 4));
      await tester.enterText(find.byType(TextFormField).first, 'testuser');
      await tester.enterText(find.byType(TextFormField).last, 'password123');
      await tester.tap(find.text('Login'));
      await tester.pumpAndSettle();

      // Test navigation to notifications (if available)
      if (find.byIcon(Icons.notifications).evaluate().isNotEmpty) {
        await tester.tap(find.byIcon(Icons.notifications));
        await tester.pumpAndSettle();
      }

      // Test quick action buttons
      if (find.text('New Goal').evaluate().isNotEmpty) {
        await tester.tap(find.text('New Goal'));
        await tester.pumpAndSettle();
      }
    });

    testWidgets('error handling', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();
      
      // Wait for splash
      await tester.pumpAndSettle(const Duration(seconds: 4));

      // Try to login with invalid credentials
      await tester.enterText(find.byType(TextFormField).first, '');
      await tester.enterText(find.byType(TextFormField).last, '');
      await tester.tap(find.text('Login'));
      await tester.pumpAndSettle();

      // Should show validation errors
      expect(find.text('Please enter your username or email'), findsOneWidget);
      expect(find.text('Please enter your password'), findsOneWidget);
    });

    testWidgets('responsive design test', (WidgetTester tester) async {
      // Test different screen sizes
      await tester.binding.setSurfaceSize(const Size(320, 568)); // iPhone SE
      
      app.main();
      await tester.pumpAndSettle();
      
      // Skip to login
      await tester.pumpAndSettle(const Duration(seconds: 4));
      
      // Check if UI elements are still visible and properly arranged
      expect(find.text('BloomBudget'), findsOneWidget);
      expect(find.text('Username or Email'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
      
      // Test tablet size
      await tester.binding.setSurfaceSize(const Size(768, 1024)); // iPad
      await tester.pumpAndSettle();
      
      // UI should still be functional
      expect(find.text('BloomBudget'), findsOneWidget);
      expect(find.text('Username or Email'), findsOneWidget);
      
      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('performance test - smooth scrolling', (WidgetTester tester) async {
      // Start the app and navigate to home
      app.main();
      await tester.pumpAndSettle();
      
      // Skip splash and login
      await tester.pumpAndSettle(const Duration(seconds: 4));
      await tester.enterText(find.byType(TextFormField).first, 'testuser');
      await tester.enterText(find.byType(TextFormField).last, 'password123');
      await tester.tap(find.text('Login'));
      await tester.pumpAndSettle();

      // Test scrolling performance
      final scrollable = find.byType(CustomScrollView);
      if (scrollable.evaluate().isNotEmpty) {
        await tester.drag(scrollable, const Offset(0, -300));
        await tester.pumpAndSettle();
        
        await tester.drag(scrollable, const Offset(0, 300));
        await tester.pumpAndSettle();
      }
      
      // No specific assertions needed - test passes if no exceptions thrown
    });
  });
}
