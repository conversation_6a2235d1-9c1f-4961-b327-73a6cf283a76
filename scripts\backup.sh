#!/bin/bash

# BloomBudget Backup Script
# This script creates backups of the database and uploaded files

set -euo pipefail

# Configuration
BACKUP_DIR="/opt/backups/bloom-budget"
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-3306}"
DB_NAME="${DB_NAME:-bloom_budget_db}"
DB_USER="${DB_USER:-bloom_budget_user}"
DB_PASSWORD="${DB_PASSWORD:-secure_password_123}"
UPLOADS_DIR="${UPLOADS_DIR:-/opt/bloom-budget/uploads}"
RETENTION_DAYS="${RETENTION_DAYS:-30}"
S3_BUCKET="${S3_BUCKET:-bloom-budget-backups}"
SLACK_WEBHOOK="${SLACK_WEBHOOK:-}"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Generate timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="bloom_budget_backup_$TIMESTAMP"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$BACKUP_DIR/backup.log"
}

# Slack notification function
notify_slack() {
    local message="$1"
    local color="${2:-good}"
    
    if [[ -n "$SLACK_WEBHOOK" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"attachments\":[{\"color\":\"$color\",\"text\":\"$message\"}]}" \
            "$SLACK_WEBHOOK" || true
    fi
}

# Error handler
error_handler() {
    local line_number=$1
    log "ERROR: Backup failed at line $line_number"
    notify_slack "❌ BloomBudget backup failed at line $line_number" "danger"
    exit 1
}

trap 'error_handler $LINENO' ERR

log "Starting BloomBudget backup process..."

# 1. Database Backup
log "Creating database backup..."
DB_BACKUP_FILE="$BACKUP_DIR/${BACKUP_NAME}_database.sql"

mysqldump \
    --host="$DB_HOST" \
    --port="$DB_PORT" \
    --user="$DB_USER" \
    --password="$DB_PASSWORD" \
    --single-transaction \
    --routines \
    --triggers \
    --events \
    --add-drop-database \
    --databases "$DB_NAME" \
    > "$DB_BACKUP_FILE"

# Compress database backup
gzip "$DB_BACKUP_FILE"
DB_BACKUP_FILE="${DB_BACKUP_FILE}.gz"

log "Database backup completed: $DB_BACKUP_FILE"

# 2. Files Backup
if [[ -d "$UPLOADS_DIR" ]]; then
    log "Creating files backup..."
    FILES_BACKUP_FILE="$BACKUP_DIR/${BACKUP_NAME}_files.tar.gz"
    
    tar -czf "$FILES_BACKUP_FILE" -C "$(dirname "$UPLOADS_DIR")" "$(basename "$UPLOADS_DIR")"
    
    log "Files backup completed: $FILES_BACKUP_FILE"
else
    log "WARNING: Uploads directory not found: $UPLOADS_DIR"
fi

# 3. Configuration Backup
log "Creating configuration backup..."
CONFIG_BACKUP_FILE="$BACKUP_DIR/${BACKUP_NAME}_config.tar.gz"

# Create temporary directory for config files
TEMP_CONFIG_DIR=$(mktemp -d)
trap "rm -rf $TEMP_CONFIG_DIR" EXIT

# Copy configuration files
if [[ -f "/opt/bloom-budget/docker-compose.yml" ]]; then
    cp "/opt/bloom-budget/docker-compose.yml" "$TEMP_CONFIG_DIR/"
fi

if [[ -f "/opt/bloom-budget/.env" ]]; then
    # Remove sensitive data from .env backup
    grep -v -E "(PASSWORD|SECRET|KEY)" "/opt/bloom-budget/.env" > "$TEMP_CONFIG_DIR/.env" || true
fi

if [[ -d "/opt/bloom-budget/nginx" ]]; then
    cp -r "/opt/bloom-budget/nginx" "$TEMP_CONFIG_DIR/"
fi

tar -czf "$CONFIG_BACKUP_FILE" -C "$TEMP_CONFIG_DIR" .

log "Configuration backup completed: $CONFIG_BACKUP_FILE"

# 4. Upload to S3 (if configured)
if command -v aws &> /dev/null && [[ -n "$S3_BUCKET" ]]; then
    log "Uploading backups to S3..."
    
    aws s3 cp "$DB_BACKUP_FILE" "s3://$S3_BUCKET/database/"
    
    if [[ -f "$FILES_BACKUP_FILE" ]]; then
        aws s3 cp "$FILES_BACKUP_FILE" "s3://$S3_BUCKET/files/"
    fi
    
    aws s3 cp "$CONFIG_BACKUP_FILE" "s3://$S3_BUCKET/config/"
    
    log "S3 upload completed"
else
    log "S3 upload skipped (AWS CLI not available or S3_BUCKET not set)"
fi

# 5. Cleanup old backups
log "Cleaning up old backups (older than $RETENTION_DAYS days)..."
find "$BACKUP_DIR" -name "bloom_budget_backup_*" -type f -mtime +$RETENTION_DAYS -delete

# 6. Verify backup integrity
log "Verifying backup integrity..."

# Check if database backup is valid
if gzip -t "$DB_BACKUP_FILE"; then
    log "Database backup integrity check: PASSED"
else
    log "ERROR: Database backup integrity check: FAILED"
    exit 1
fi

# Check if files backup is valid (if exists)
if [[ -f "$FILES_BACKUP_FILE" ]] && tar -tzf "$FILES_BACKUP_FILE" > /dev/null; then
    log "Files backup integrity check: PASSED"
elif [[ -f "$FILES_BACKUP_FILE" ]]; then
    log "ERROR: Files backup integrity check: FAILED"
    exit 1
fi

# Check if config backup is valid
if tar -tzf "$CONFIG_BACKUP_FILE" > /dev/null; then
    log "Configuration backup integrity check: PASSED"
else
    log "ERROR: Configuration backup integrity check: FAILED"
    exit 1
fi

# 7. Generate backup report
BACKUP_SIZE=$(du -sh "$BACKUP_DIR/${BACKUP_NAME}"* | awk '{sum+=$1} END {print sum}')
BACKUP_COUNT=$(ls -1 "$BACKUP_DIR"/bloom_budget_backup_* | wc -l)

REPORT="✅ BloomBudget backup completed successfully!
📅 Timestamp: $TIMESTAMP
💾 Database backup: $(basename "$DB_BACKUP_FILE")
📁 Files backup: $(basename "$FILES_BACKUP_FILE" 2>/dev/null || echo "N/A")
⚙️ Config backup: $(basename "$CONFIG_BACKUP_FILE")
📊 Total size: ${BACKUP_SIZE}MB
🗂️ Total backups: $BACKUP_COUNT
🗑️ Retention: $RETENTION_DAYS days"

log "$REPORT"
notify_slack "$REPORT"

log "Backup process completed successfully!"

# 8. Optional: Test restore process
if [[ "${TEST_RESTORE:-false}" == "true" ]]; then
    log "Testing restore process..."
    
    # Create test database
    TEST_DB_NAME="${DB_NAME}_restore_test"
    
    mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" \
        -e "DROP DATABASE IF EXISTS $TEST_DB_NAME; CREATE DATABASE $TEST_DB_NAME;"
    
    # Restore backup to test database
    zcat "$DB_BACKUP_FILE" | sed "s/$DB_NAME/$TEST_DB_NAME/g" | \
        mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD"
    
    # Verify restore
    TABLE_COUNT=$(mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" \
        -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$TEST_DB_NAME';" \
        -s -N)
    
    if [[ "$TABLE_COUNT" -gt 0 ]]; then
        log "Restore test: PASSED ($TABLE_COUNT tables restored)"
    else
        log "ERROR: Restore test: FAILED (no tables found)"
        exit 1
    fi
    
    # Cleanup test database
    mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" \
        -e "DROP DATABASE $TEST_DB_NAME;"
    
    log "Restore test completed successfully"
fi

exit 0
