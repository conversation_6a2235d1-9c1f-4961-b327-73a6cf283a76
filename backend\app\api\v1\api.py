from fastapi import APIRouter
from .endpoints import (
    auth,
    users,
    savings_goals,
    rose_garden,
    digital_safe,
    challenges,
    chat,
    flexible_plans,
    transactions
)

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(savings_goals.router, prefix="/savings-goals", tags=["savings-goals"])
api_router.include_router(rose_garden.router, prefix="/rose-garden", tags=["rose-garden"])
api_router.include_router(digital_safe.router, prefix="/digital-safe", tags=["digital-safe"])
api_router.include_router(challenges.router, prefix="/challenges", tags=["challenges"])
api_router.include_router(chat.router, prefix="/chat", tags=["chat"])
api_router.include_router(flexible_plans.router, prefix="/flexible-plans", tags=["flexible-plans"])
api_router.include_router(transactions.router, prefix="/transactions", tags=["transactions"])
