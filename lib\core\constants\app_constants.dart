class AppConstants {
  // App Information
  static const String appName = 'BloomBudget';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const String baseUrl = 'http://localhost:8000/api/v1';
  static const Duration apiTimeout = Duration(seconds: 30);
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String languageKey = 'language';
  static const String themeKey = 'theme';
  
  // Rose Garden Constants
  static const int basicRoseThreshold = 100;
  static const int premiumRoseThreshold = 500;
  static const int luxuryRoseThreshold = 1000;
  
  // Digital Safe Constants
  static const int minSafeAmount = 50;
  static const int maxSafeAmount = 10000;
  static const int safeGameAttempts = 3;
  
  // Challenge Constants
  static const int minChallengeParticipants = 2;
  static const int maxChallengeParticipants = 10;
  static const int challengeDurationDays = 30;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 300);
  static const Duration mediumAnimation = Duration(milliseconds: 500);
  static const Duration longAnimation = Duration(milliseconds: 800);
  
  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 50;
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 20;
}
