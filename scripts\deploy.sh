#!/bin/bash

# BloomBudget Deployment Script
# This script handles deployment to different environments

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENTS=("development" "staging" "production")
DEFAULT_ENVIRONMENT="staging"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Usage function
usage() {
    echo "BloomBudget Deployment Script"
    echo ""
    echo "Usage: $0 [OPTIONS] [ENVIRONMENT]"
    echo ""
    echo "Environments:"
    echo "  development    Deploy to development environment"
    echo "  staging        Deploy to staging environment (default)"
    echo "  production     Deploy to production environment"
    echo ""
    echo "Options:"
    echo "  -b, --build-only       Build images without deploying"
    echo "  -d, --deploy-only      Deploy without building (use existing images)"
    echo "  -f, --force            Force deployment without confirmation"
    echo "  -r, --rollback         Rollback to previous version"
    echo "  -h, --help             Show this help message"
    echo "  -v, --verbose          Enable verbose output"
    echo "  --skip-tests           Skip running tests before deployment"
    echo "  --skip-backup          Skip creating backup before deployment"
    echo ""
    echo "Examples:"
    echo "  $0 staging                    # Deploy to staging"
    echo "  $0 production -f              # Force deploy to production"
    echo "  $0 -b                         # Build images only"
    echo "  $0 -r production              # Rollback production"
    exit 1
}

# Parse command line arguments
BUILD_ONLY=false
DEPLOY_ONLY=false
FORCE_DEPLOY=false
ROLLBACK=false
VERBOSE=false
SKIP_TESTS=false
SKIP_BACKUP=false
ENVIRONMENT=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -b|--build-only)
            BUILD_ONLY=true
            shift
            ;;
        -d|--deploy-only)
            DEPLOY_ONLY=true
            shift
            ;;
        -f|--force)
            FORCE_DEPLOY=true
            shift
            ;;
        -r|--rollback)
            ROLLBACK=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            set -x
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --skip-backup)
            SKIP_BACKUP=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        development|staging|production)
            ENVIRONMENT="$1"
            shift
            ;;
        *)
            error "Unknown option: $1"
            usage
            ;;
    esac
done

# Set default environment
if [[ -z "$ENVIRONMENT" ]]; then
    ENVIRONMENT="$DEFAULT_ENVIRONMENT"
fi

# Validate environment
if [[ ! " ${ENVIRONMENTS[@]} " =~ " ${ENVIRONMENT} " ]]; then
    error "Invalid environment: $ENVIRONMENT"
    error "Valid environments: ${ENVIRONMENTS[*]}"
    exit 1
fi

log "Starting BloomBudget deployment to $ENVIRONMENT environment"

# Load environment-specific configuration
ENV_FILE="$PROJECT_ROOT/environments/$ENVIRONMENT/.env"
if [[ -f "$ENV_FILE" ]]; then
    log "Loading environment configuration: $ENV_FILE"
    set -a
    source "$ENV_FILE"
    set +a
else
    warning "Environment file not found: $ENV_FILE"
fi

# Pre-deployment checks
pre_deployment_checks() {
    log "Running pre-deployment checks..."
    
    # Check if required tools are installed
    local required_tools=("docker" "docker-compose")
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        required_tools+=("kubectl")
    fi
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            error "$tool is required but not installed"
            exit 1
        fi
    done
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        error "Docker is not running"
        exit 1
    fi
    
    # Check if we're on the correct branch for production
    if [[ "$ENVIRONMENT" == "production" ]]; then
        current_branch=$(git rev-parse --abbrev-ref HEAD)
        if [[ "$current_branch" != "main" ]]; then
            error "Production deployments must be from 'main' branch (currently on '$current_branch')"
            exit 1
        fi
        
        # Check if working directory is clean
        if [[ -n "$(git status --porcelain)" ]]; then
            error "Working directory is not clean. Commit or stash changes before production deployment"
            exit 1
        fi
    fi
    
    success "Pre-deployment checks passed"
}

# Run tests
run_tests() {
    if [[ "$SKIP_TESTS" == "true" ]]; then
        warning "Skipping tests as requested"
        return
    fi
    
    log "Running tests..."
    
    # Run Flutter tests
    log "Running Flutter tests..."
    cd "$PROJECT_ROOT"
    flutter test || {
        error "Flutter tests failed"
        exit 1
    }
    
    # Run backend tests
    log "Running backend tests..."
    cd "$PROJECT_ROOT/backend"
    python -m pytest tests/ -v || {
        error "Backend tests failed"
        exit 1
    }
    
    success "All tests passed"
}

# Build images
build_images() {
    if [[ "$DEPLOY_ONLY" == "true" ]]; then
        log "Skipping build as requested"
        return
    fi
    
    log "Building Docker images..."
    
    cd "$PROJECT_ROOT"
    
    # Build backend image
    log "Building backend image..."
    docker build -t "bloom-budget-backend:$ENVIRONMENT" backend/
    
    # Tag with latest for the environment
    docker tag "bloom-budget-backend:$ENVIRONMENT" "bloom-budget-backend:$ENVIRONMENT-latest"
    
    # Tag with git commit hash
    GIT_HASH=$(git rev-parse --short HEAD)
    docker tag "bloom-budget-backend:$ENVIRONMENT" "bloom-budget-backend:$ENVIRONMENT-$GIT_HASH"
    
    success "Images built successfully"
}

# Create backup
create_backup() {
    if [[ "$SKIP_BACKUP" == "true" || "$ENVIRONMENT" == "development" ]]; then
        log "Skipping backup"
        return
    fi
    
    log "Creating pre-deployment backup..."
    
    if [[ -f "$PROJECT_ROOT/scripts/backup.sh" ]]; then
        bash "$PROJECT_ROOT/scripts/backup.sh"
        success "Backup created successfully"
    else
        warning "Backup script not found, skipping backup"
    fi
}

# Deploy to environment
deploy() {
    if [[ "$BUILD_ONLY" == "true" ]]; then
        log "Skipping deployment as requested"
        return
    fi
    
    log "Deploying to $ENVIRONMENT environment..."
    
    case "$ENVIRONMENT" in
        development|staging)
            deploy_docker_compose
            ;;
        production)
            deploy_kubernetes
            ;;
    esac
    
    success "Deployment completed"
}

# Deploy using Docker Compose
deploy_docker_compose() {
    log "Deploying using Docker Compose..."
    
    cd "$PROJECT_ROOT"
    
    # Use environment-specific compose file if it exists
    COMPOSE_FILE="docker-compose.yml"
    ENV_COMPOSE_FILE="environments/$ENVIRONMENT/docker-compose.override.yml"
    
    COMPOSE_ARGS="-f $COMPOSE_FILE"
    if [[ -f "$ENV_COMPOSE_FILE" ]]; then
        COMPOSE_ARGS="$COMPOSE_ARGS -f $ENV_COMPOSE_FILE"
    fi
    
    # Pull latest images (if not building locally)
    if [[ "$DEPLOY_ONLY" == "true" ]]; then
        docker-compose $COMPOSE_ARGS pull
    fi
    
    # Deploy services
    docker-compose $COMPOSE_ARGS up -d
    
    # Wait for services to be healthy
    log "Waiting for services to be healthy..."
    sleep 30
    
    # Health check
    if curl -f "http://localhost:8000/health" &> /dev/null; then
        success "Health check passed"
    else
        error "Health check failed"
        exit 1
    fi
}

# Deploy to Kubernetes
deploy_kubernetes() {
    log "Deploying to Kubernetes..."
    
    cd "$PROJECT_ROOT"
    
    # Apply Kubernetes manifests
    kubectl apply -f k8s/namespace.yaml
    kubectl apply -f k8s/configmap.yaml
    kubectl apply -f k8s/mysql-deployment.yaml
    kubectl apply -f k8s/backend-deployment.yaml
    
    # Wait for deployment to complete
    log "Waiting for deployment to complete..."
    kubectl rollout status deployment/backend-deployment -n bloom-budget --timeout=300s
    
    # Health check
    log "Performing health check..."
    kubectl wait --for=condition=ready pod -l app=backend -n bloom-budget --timeout=300s
    
    success "Kubernetes deployment completed"
}

# Rollback deployment
rollback() {
    log "Rolling back $ENVIRONMENT deployment..."
    
    case "$ENVIRONMENT" in
        development|staging)
            rollback_docker_compose
            ;;
        production)
            rollback_kubernetes
            ;;
    esac
    
    success "Rollback completed"
}

# Rollback Docker Compose deployment
rollback_docker_compose() {
    log "Rolling back Docker Compose deployment..."
    
    cd "$PROJECT_ROOT"
    
    # Get previous image tag
    PREVIOUS_TAG=$(docker images --format "table {{.Repository}}:{{.Tag}}" | grep "bloom-budget-backend:$ENVIRONMENT-" | head -2 | tail -1 | cut -d: -f2)
    
    if [[ -n "$PREVIOUS_TAG" ]]; then
        log "Rolling back to tag: $PREVIOUS_TAG"
        
        # Update docker-compose to use previous tag
        sed -i.bak "s/bloom-budget-backend:$ENVIRONMENT-latest/bloom-budget-backend:$PREVIOUS_TAG/" docker-compose.yml
        
        # Redeploy
        docker-compose up -d backend
        
        # Restore original compose file
        mv docker-compose.yml.bak docker-compose.yml
    else
        error "No previous version found for rollback"
        exit 1
    fi
}

# Rollback Kubernetes deployment
rollback_kubernetes() {
    log "Rolling back Kubernetes deployment..."
    
    kubectl rollout undo deployment/backend-deployment -n bloom-budget
    kubectl rollout status deployment/backend-deployment -n bloom-budget --timeout=300s
}

# Post-deployment tasks
post_deployment() {
    log "Running post-deployment tasks..."
    
    # Run database migrations if needed
    if [[ "$ENVIRONMENT" != "development" ]]; then
        log "Running database migrations..."
        # Add migration commands here
    fi
    
    # Clear caches
    log "Clearing application caches..."
    # Add cache clearing commands here
    
    # Send deployment notification
    send_notification
    
    success "Post-deployment tasks completed"
}

# Send deployment notification
send_notification() {
    if [[ -n "${SLACK_WEBHOOK:-}" ]]; then
        local message="🚀 BloomBudget deployed to $ENVIRONMENT successfully!"
        local git_hash=$(git rev-parse --short HEAD)
        local git_message=$(git log -1 --pretty=%B)
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"$message\n\n**Commit:** $git_hash\n**Message:** $git_message\"}" \
            "$SLACK_WEBHOOK" || true
    fi
}

# Confirmation prompt
confirm_deployment() {
    if [[ "$FORCE_DEPLOY" == "true" ]]; then
        return
    fi
    
    echo ""
    echo "🚀 Deployment Summary:"
    echo "  Environment: $ENVIRONMENT"
    echo "  Build: $([ "$BUILD_ONLY" == "true" ] && echo "Only" || [ "$DEPLOY_ONLY" == "true" ] && echo "Skip" || echo "Yes")"
    echo "  Deploy: $([ "$DEPLOY_ONLY" == "true" ] && echo "Only" || [ "$BUILD_ONLY" == "true" ] && echo "Skip" || echo "Yes")"
    echo "  Tests: $([ "$SKIP_TESTS" == "true" ] && echo "Skip" || echo "Run")"
    echo "  Backup: $([ "$SKIP_BACKUP" == "true" ] && echo "Skip" || echo "Create")"
    echo ""
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        echo "⚠️  WARNING: This is a PRODUCTION deployment!"
        echo ""
    fi
    
    read -p "Do you want to continue? (yes/no): " -r
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        log "Deployment cancelled by user"
        exit 0
    fi
}

# Main execution
main() {
    if [[ "$ROLLBACK" == "true" ]]; then
        rollback
        exit 0
    fi
    
    confirm_deployment
    pre_deployment_checks
    run_tests
    create_backup
    build_images
    deploy
    post_deployment
    
    success "🎉 BloomBudget deployment to $ENVIRONMENT completed successfully!"
}

# Run main function
main "$@"
