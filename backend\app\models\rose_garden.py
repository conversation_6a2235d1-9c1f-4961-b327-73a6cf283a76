from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, Text, ForeignKey, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..core.database import Base

class RoseGarden(Base):
    __tablename__ = "rose_gardens"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, unique=True)
    
    # Garden details
    garden_name = Column(String(100), default="My Rose Garden")
    total_roses = Column(Integer, default=0)
    garden_level = Column(Integer, default=1)
    
    # Rose types count
    basic_roses = Column(Integer, default=0)      # For savings 100-499 SAR
    premium_roses = Column(Integer, default=0)    # For savings 500-999 SAR
    luxury_roses = Column(Integer, default=0)     # For savings 1000+ SAR
    
    # Garden customization
    background_theme = Column(String(50), default="default")
    garden_layout = Column(JSON)  # Store garden layout configuration
    
    # Social features
    is_public = Column(Boolean, default=False)
    share_count = Column(Integer, default=0)
    likes_count = Column(Integer, default=0)
    
    # Statistics
    total_savings_contributed = Column(Float, default=0.0)
    last_rose_added = Column(DateTime(timezone=True))
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="rose_garden")
    roses = relationship("Rose", back_populates="garden")
    
    @property
    def garden_completion_percentage(self):
        # Calculate based on garden level and total roses
        expected_roses_for_level = self.garden_level * 10
        if expected_roses_for_level == 0:
            return 0
        return min((self.total_roses / expected_roses_for_level) * 100, 100)
    
    def add_rose(self, savings_amount):
        """Add a rose based on savings amount"""
        if savings_amount >= 1000:
            self.luxury_roses += 1
            rose_type = "luxury"
        elif savings_amount >= 500:
            self.premium_roses += 1
            rose_type = "premium"
        else:
            self.basic_roses += 1
            rose_type = "basic"
        
        self.total_roses += 1
        self.total_savings_contributed += savings_amount
        self.last_rose_added = func.now()
        
        # Level up logic
        if self.total_roses >= (self.garden_level * 10):
            self.garden_level += 1
        
        return rose_type

    def __repr__(self):
        return f"<RoseGarden(id={self.id}, user_id={self.user_id}, total_roses={self.total_roses})>"

class Rose(Base):
    __tablename__ = "roses"

    id = Column(Integer, primary_key=True, index=True)
    garden_id = Column(Integer, ForeignKey("rose_gardens.id"), nullable=False)
    
    # Rose details
    rose_type = Column(String(20), nullable=False)  # basic, premium, luxury
    savings_amount = Column(Float, nullable=False)
    position_x = Column(Float)  # Position in garden
    position_y = Column(Float)
    
    # Visual properties
    color = Column(String(7))  # Hex color
    size = Column(String(10), default="medium")  # small, medium, large
    bloom_stage = Column(Integer, default=1)  # 1-5 bloom stages
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    bloomed_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    garden = relationship("RoseGarden", back_populates="roses")

    def __repr__(self):
        return f"<Rose(id={self.id}, type='{self.rose_type}', amount={self.savings_amount})>"
