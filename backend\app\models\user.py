from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..core.database import Base

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    phone_number = Column(String(20), unique=True, index=True)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=False)
    date_of_birth = Column(DateTime)
    
    # Profile information
    profile_picture = Column(String(255))
    preferred_language = Column(String(10), default="ar")
    
    # Account status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    verification_token = Column(String(255))
    
    # Financial information
    total_savings = Column(Float, default=0.0)
    current_balance = Column(Float, default=0.0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True))
    
    # Relationships
    savings_goals = relationship("SavingsGoal", back_populates="user")
    rose_garden = relationship("RoseGarden", back_populates="user", uselist=False)
    digital_safes = relationship("DigitalSafe", back_populates="user")
    challenges_created = relationship("Challenge", foreign_keys="Challenge.creator_id", back_populates="creator")
    challenge_participations = relationship("ChallengeParticipant", back_populates="user")
    chat_sessions = relationship("ChatSession", back_populates="user")
    flexible_plans = relationship("FlexiblePlan", back_populates="user")
    transactions = relationship("Transaction", back_populates="user")

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
