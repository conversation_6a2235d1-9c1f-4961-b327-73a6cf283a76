import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';

class TransactionItem extends StatelessWidget {
  final Map<String, dynamic> transaction;

  const TransactionItem({
    super.key,
    required this.transaction,
  });

  @override
  Widget build(BuildContext context) {
    final type = transaction['type'] as String;
    final amount = transaction['amount'] as double;
    final description = transaction['description'] as String;
    final date = transaction['date'] as DateTime;

    IconData icon;
    Color iconColor;

    switch (type) {
      case 'deposit':
        icon = Icons.add_circle;
        iconColor = AppTheme.successColor;
        break;
      case 'withdrawal':
        icon = Icons.remove_circle;
        iconColor = AppTheme.errorColor;
        break;
      case 'goal_contribution':
        icon = Icons.flag;
        iconColor = AppTheme.accentColor;
        break;
      case 'safe_lock':
        icon = Icons.lock;
        iconColor = AppTheme.warningColor;
        break;
      default:
        icon = Icons.swap_horiz;
        iconColor = AppTheme.secondaryColor;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.hintTextColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppTheme.primaryTextColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _formatDate(date),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${type == 'withdrawal' ? '-' : '+'}${amount.toStringAsFixed(0)} SAR',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: type == 'withdrawal' ? AppTheme.errorColor : AppTheme.successColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
