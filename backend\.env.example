# Database Configuration
DATABASE_URL=mysql+mysqlconnector://username:password@localhost:3306/bloom_budget_db
DB_HOST=localhost
DB_PORT=3306
DB_USER=username
DB_PASSWORD=password
DB_NAME=bloom_budget_db

# JWT Configuration
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379/0

# OpenAI Configuration (for AI chat)
OPENAI_API_KEY=your-openai-api-key-here

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# App Configuration
APP_NAME=BloomBudget
APP_VERSION=1.0.0
DEBUG=True
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=uploads/

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
