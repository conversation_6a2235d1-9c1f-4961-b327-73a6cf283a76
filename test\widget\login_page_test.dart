import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:bloom_budget_app/features/auth/presentation/pages/login_page.dart';
import 'package:bloom_budget_app/features/auth/presentation/providers/auth_provider.dart';
import 'package:bloom_budget_app/core/theme/app_theme.dart';

void main() {
  group('LoginPage Widget Tests', () {
    late AuthProvider authProvider;

    setUp(() {
      authProvider = AuthProvider();
    });

    Widget createTestWidget() {
      return MaterialApp(
        theme: AppTheme.darkTheme,
        home: ChangeNotifierProvider<AuthProvider>.value(
          value: authProvider,
          child: const LoginPage(),
        ),
      );
    }

    testWidgets('should display all login form elements', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Check if logo is displayed
      expect(find.byIcon(Icons.savings), findsOneWidget);
      
      // Check if app name is displayed
      expect(find.text('BloomBudget'), findsOneWidget);
      
      // Check if tagline is displayed
      expect(find.text('Smart Savings for Smart Youth'), findsOneWidget);
      
      // Check if username field is displayed
      expect(find.byType(TextFormField), findsNWidgets(2));
      expect(find.text('Username or Email'), findsOneWidget);
      
      // Check if password field is displayed
      expect(find.text('Password'), findsOneWidget);
      
      // Check if login button is displayed
      expect(find.text('Login'), findsOneWidget);
      
      // Check if forgot password link is displayed
      expect(find.text('Forgot Password?'), findsOneWidget);
      
      // Check if sign up link is displayed
      expect(find.text('Sign Up'), findsOneWidget);
    });

    testWidgets('should show validation errors for empty fields', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Tap login button without entering credentials
      await tester.tap(find.text('Login'));
      await tester.pump();

      // Check for validation error messages
      expect(find.text('Please enter your username or email'), findsOneWidget);
      expect(find.text('Please enter your password'), findsOneWidget);
    });

    testWidgets('should toggle password visibility', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find password field
      final passwordField = find.byType(TextFormField).last;
      final visibilityToggle = find.byIcon(Icons.visibility);

      // Initially password should be obscured
      expect(tester.widget<TextFormField>(passwordField).obscureText, true);
      expect(visibilityToggle, findsOneWidget);

      // Tap visibility toggle
      await tester.tap(visibilityToggle);
      await tester.pump();

      // Password should now be visible
      expect(tester.widget<TextFormField>(passwordField).obscureText, false);
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
    });

    testWidgets('should show loading indicator during login', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Enter valid credentials
      await tester.enterText(find.byType(TextFormField).first, 'testuser');
      await tester.enterText(find.byType(TextFormField).last, 'password123');

      // Tap login button
      await tester.tap(find.text('Login'));
      await tester.pump();

      // Should show loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should display error message on login failure', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Enter invalid credentials (empty)
      await tester.enterText(find.byType(TextFormField).first, '');
      await tester.enterText(find.byType(TextFormField).last, '');

      // Tap login button
      await tester.tap(find.text('Login'));
      await tester.pump();
      
      // Wait for login to complete
      await tester.pumpAndSettle();

      // Should display error message
      expect(find.byIcon(Icons.error), findsOneWidget);
      expect(find.text('Invalid username or password'), findsOneWidget);
    });

    testWidgets('should navigate to home on successful login', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Enter valid credentials
      await tester.enterText(find.byType(TextFormField).first, 'testuser');
      await tester.enterText(find.byType(TextFormField).last, 'password123');

      // Tap login button
      await tester.tap(find.text('Login'));
      await tester.pump();
      
      // Wait for login to complete and navigation
      await tester.pumpAndSettle();

      // Should navigate away from login page
      expect(find.text('Login'), findsNothing);
    });

    testWidgets('should have proper styling and theme', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Check background color
      final scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
      expect(scaffold.backgroundColor, AppTheme.backgroundColor);

      // Check if form fields have proper styling
      final textFields = find.byType(TextFormField);
      expect(textFields, findsNWidgets(2));

      // Check if buttons have proper styling
      final loginButton = find.byType(ElevatedButton);
      expect(loginButton, findsOneWidget);
    });

    testWidgets('should handle keyboard input correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Enter text in username field
      await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
      expect(find.text('<EMAIL>'), findsOneWidget);

      // Enter text in password field
      await tester.enterText(find.byType(TextFormField).last, 'mypassword');
      
      // Password should be obscured, so we check the controller value instead
      final passwordField = tester.widget<TextFormField>(find.byType(TextFormField).last);
      expect(passwordField.controller?.text, 'mypassword');
    });
  });
}
