import pytest
from datetime import datetime, timedelta
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.main import app
from app.core.database import get_db, Base
from app.models.user import User
from app.models.savings_goal import SavingsGoal, GoalStatus, GoalCategory

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_goals.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture(scope="module")
def client():
    Base.metadata.create_all(bind=engine)
    with TestClient(app) as c:
        yield c
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def authenticated_user(client):
    """Create and authenticate a test user"""
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "full_name": "Test User",
        "password": "testpassword123"
    }
    
    # Register user
    client.post("/api/v1/auth/register", json=user_data)
    
    # Login and get token
    login_response = client.post("/api/v1/auth/login", data={
        "username": user_data["username"],
        "password": user_data["password"]
    })
    token = login_response.json()["access_token"]
    
    return {"Authorization": f"Bearer {token}"}

@pytest.fixture
def sample_goal_data():
    return {
        "title": "New iPhone",
        "description": "Save for the latest iPhone",
        "category": "phone",
        "target_amount": 5000.0,
        "target_date": (datetime.now() + timedelta(days=90)).isoformat(),
        "priority": 1,
        "is_public": False
    }

class TestSavingsGoals:
    def test_create_savings_goal_success(self, client, authenticated_user, sample_goal_data):
        """Test successful creation of savings goal"""
        response = client.post(
            "/api/v1/savings-goals/",
            json=sample_goal_data,
            headers=authenticated_user
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == sample_goal_data["title"]
        assert data["target_amount"] == sample_goal_data["target_amount"]
        assert data["current_amount"] == 0.0
        assert data["status"] == "active"
        assert "id" in data
        assert "weekly_target" in data
        assert "monthly_target" in data

    def test_create_goal_invalid_data(self, client, authenticated_user):
        """Test creating goal with invalid data"""
        invalid_data = {
            "title": "",  # Empty title
            "target_amount": -100,  # Negative amount
            "target_date": "2020-01-01T00:00:00"  # Past date
        }
        
        response = client.post(
            "/api/v1/savings-goals/",
            json=invalid_data,
            headers=authenticated_user
        )
        
        assert response.status_code == 422

    def test_get_savings_goals(self, client, authenticated_user, sample_goal_data):
        """Test retrieving user's savings goals"""
        # Create a goal first
        client.post(
            "/api/v1/savings-goals/",
            json=sample_goal_data,
            headers=authenticated_user
        )
        
        # Get goals
        response = client.get("/api/v1/savings-goals/", headers=authenticated_user)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        assert data[0]["title"] == sample_goal_data["title"]

    def test_get_goals_by_status(self, client, authenticated_user, sample_goal_data):
        """Test filtering goals by status"""
        # Create a goal
        client.post(
            "/api/v1/savings-goals/",
            json=sample_goal_data,
            headers=authenticated_user
        )
        
        # Get active goals
        response = client.get(
            "/api/v1/savings-goals/?status=active",
            headers=authenticated_user
        )
        
        assert response.status_code == 200
        data = response.json()
        assert all(goal["status"] == "active" for goal in data)

    def test_get_goals_by_category(self, client, authenticated_user, sample_goal_data):
        """Test filtering goals by category"""
        # Create a goal
        client.post(
            "/api/v1/savings-goals/",
            json=sample_goal_data,
            headers=authenticated_user
        )
        
        # Get phone category goals
        response = client.get(
            "/api/v1/savings-goals/?category=phone",
            headers=authenticated_user
        )
        
        assert response.status_code == 200
        data = response.json()
        assert all(goal["category"] == "phone" for goal in data)

    def test_get_specific_goal(self, client, authenticated_user, sample_goal_data):
        """Test retrieving a specific savings goal"""
        # Create a goal
        create_response = client.post(
            "/api/v1/savings-goals/",
            json=sample_goal_data,
            headers=authenticated_user
        )
        goal_id = create_response.json()["id"]
        
        # Get specific goal
        response = client.get(
            f"/api/v1/savings-goals/{goal_id}",
            headers=authenticated_user
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == goal_id
        assert data["title"] == sample_goal_data["title"]

    def test_get_nonexistent_goal(self, client, authenticated_user):
        """Test retrieving non-existent goal"""
        response = client.get("/api/v1/savings-goals/999", headers=authenticated_user)
        assert response.status_code == 404

    def test_update_savings_goal(self, client, authenticated_user, sample_goal_data):
        """Test updating a savings goal"""
        # Create a goal
        create_response = client.post(
            "/api/v1/savings-goals/",
            json=sample_goal_data,
            headers=authenticated_user
        )
        goal_id = create_response.json()["id"]
        
        # Update goal
        update_data = {
            "title": "Updated iPhone Goal",
            "target_amount": 6000.0,
            "priority": 2
        }
        
        response = client.put(
            f"/api/v1/savings-goals/{goal_id}",
            json=update_data,
            headers=authenticated_user
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == update_data["title"]
        assert data["target_amount"] == update_data["target_amount"]
        assert data["priority"] == update_data["priority"]

    def test_contribute_to_goal(self, client, authenticated_user, sample_goal_data):
        """Test contributing money to a savings goal"""
        # Create a goal
        create_response = client.post(
            "/api/v1/savings-goals/",
            json=sample_goal_data,
            headers=authenticated_user
        )
        goal_id = create_response.json()["id"]
        
        # Contribute to goal
        contribution_amount = 1000.0
        response = client.post(
            f"/api/v1/savings-goals/{goal_id}/contribute?amount={contribution_amount}",
            headers=authenticated_user
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert data["goal"]["current_amount"] == contribution_amount
        assert data["new_progress_percentage"] > 0

    def test_contribute_invalid_amount(self, client, authenticated_user, sample_goal_data):
        """Test contributing invalid amount to goal"""
        # Create a goal
        create_response = client.post(
            "/api/v1/savings-goals/",
            json=sample_goal_data,
            headers=authenticated_user
        )
        goal_id = create_response.json()["id"]
        
        # Try to contribute negative amount
        response = client.post(
            f"/api/v1/savings-goals/{goal_id}/contribute?amount=-100",
            headers=authenticated_user
        )
        
        assert response.status_code == 400
        assert "Amount must be positive" in response.json()["detail"]

    def test_goal_completion(self, client, authenticated_user, sample_goal_data):
        """Test goal completion when target is reached"""
        # Create a goal
        create_response = client.post(
            "/api/v1/savings-goals/",
            json=sample_goal_data,
            headers=authenticated_user
        )
        goal_id = create_response.json()["id"]
        
        # Contribute full amount
        response = client.post(
            f"/api/v1/savings-goals/{goal_id}/contribute?amount={sample_goal_data['target_amount']}",
            headers=authenticated_user
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["goal"]["status"] == "completed"
        assert data["goal"]["completed_date"] is not None
        assert data["new_progress_percentage"] == 100.0

    def test_delete_savings_goal(self, client, authenticated_user, sample_goal_data):
        """Test deleting a savings goal"""
        # Create a goal
        create_response = client.post(
            "/api/v1/savings-goals/",
            json=sample_goal_data,
            headers=authenticated_user
        )
        goal_id = create_response.json()["id"]
        
        # Delete goal
        response = client.delete(
            f"/api/v1/savings-goals/{goal_id}",
            headers=authenticated_user
        )
        
        assert response.status_code == 200
        assert "deleted successfully" in response.json()["message"]
        
        # Verify goal is deleted
        get_response = client.get(
            f"/api/v1/savings-goals/{goal_id}",
            headers=authenticated_user
        )
        assert get_response.status_code == 404

    def test_unauthorized_access(self, client, sample_goal_data):
        """Test accessing goals without authentication"""
        response = client.get("/api/v1/savings-goals/")
        assert response.status_code == 401

    def test_goal_progress_calculation(self, client, authenticated_user, sample_goal_data):
        """Test progress percentage calculation"""
        # Create a goal
        create_response = client.post(
            "/api/v1/savings-goals/",
            json=sample_goal_data,
            headers=authenticated_user
        )
        goal_id = create_response.json()["id"]
        
        # Contribute 25% of target
        contribution = sample_goal_data["target_amount"] * 0.25
        client.post(
            f"/api/v1/savings-goals/{goal_id}/contribute?amount={contribution}",
            headers=authenticated_user
        )
        
        # Check progress
        response = client.get(
            f"/api/v1/savings-goals/{goal_id}",
            headers=authenticated_user
        )
        
        data = response.json()
        assert abs(data["progress_percentage"] - 25.0) < 0.1  # Allow small floating point errors

    def test_goal_days_remaining(self, client, authenticated_user):
        """Test days remaining calculation"""
        future_date = datetime.now() + timedelta(days=30)
        goal_data = {
            "title": "Test Goal",
            "target_amount": 1000.0,
            "target_date": future_date.isoformat()
        }
        
        create_response = client.post(
            "/api/v1/savings-goals/",
            json=goal_data,
            headers=authenticated_user
        )
        
        data = create_response.json()
        assert data["days_remaining"] <= 30
        assert data["days_remaining"] >= 29  # Account for timing differences
