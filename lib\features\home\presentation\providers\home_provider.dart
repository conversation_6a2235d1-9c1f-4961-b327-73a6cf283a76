import 'package:flutter/material.dart';

class HomeProvider extends ChangeNotifier {
  int _selectedIndex = 0;
  bool _isLoading = false;
  String? _errorMessage;
  
  // Dashboard data
  double _totalSavings = 0.0;
  double _currentBalance = 0.0;
  int _activeGoalsCount = 0;
  int _totalRoses = 0;
  int _gardenLevel = 1;
  List<Map<String, dynamic>> _recentTransactions = [];
  List<Map<String, dynamic>> _savingsGoals = [];

  // Getters
  int get selectedIndex => _selectedIndex;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  double get totalSavings => _totalSavings;
  double get currentBalance => _currentBalance;
  int get activeGoalsCount => _activeGoalsCount;
  int get totalRoses => _totalRoses;
  int get gardenLevel => _gardenLevel;
  List<Map<String, dynamic>> get recentTransactions => _recentTransactions;
  List<Map<String, dynamic>> get savingsGoals => _savingsGoals;

  void setSelectedIndex(int index) {
    _selectedIndex = index;
    notifyListeners();
  }

  Future<void> loadDashboardData() async {
    _setLoading(true);
    _clearError();

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      // Mock data
      _totalSavings = 2500.0;
      _currentBalance = 1200.0;
      _activeGoalsCount = 3;
      _totalRoses = 15;
      _gardenLevel = 2;
      
      _recentTransactions = [
        {
          'id': 1,
          'type': 'deposit',
          'amount': 200.0,
          'description': 'Monthly savings',
          'date': DateTime.now().subtract(const Duration(days: 1)),
        },
        {
          'id': 2,
          'type': 'goal_contribution',
          'amount': 150.0,
          'description': 'Phone goal contribution',
          'date': DateTime.now().subtract(const Duration(days: 3)),
        },
      ];
      
      _savingsGoals = [
        {
          'id': 1,
          'title': 'New Phone',
          'target_amount': 3000.0,
          'current_amount': 1200.0,
          'progress': 0.4,
          'days_remaining': 45,
        },
        {
          'id': 2,
          'title': 'Travel Fund',
          'target_amount': 5000.0,
          'current_amount': 800.0,
          'progress': 0.16,
          'days_remaining': 120,
        },
      ];
      
      _setLoading(false);
    } catch (e) {
      _setError('Failed to load dashboard data: ${e.toString()}');
      _setLoading(false);
    }
  }

  Future<void> addSavings(double amount) async {
    _setLoading(true);
    _clearError();

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      _currentBalance += amount;
      _totalSavings += amount;
      
      // Add to recent transactions
      _recentTransactions.insert(0, {
        'id': DateTime.now().millisecondsSinceEpoch,
        'type': 'deposit',
        'amount': amount,
        'description': 'Savings deposit',
        'date': DateTime.now(),
      });
      
      // Check if we should add a rose
      if (amount >= 100) {
        _totalRoses++;
        if (_totalRoses >= (_gardenLevel * 10)) {
          _gardenLevel++;
        }
      }
      
      _setLoading(false);
    } catch (e) {
      _setError('Failed to add savings: ${e.toString()}');
      _setLoading(false);
    }
  }

  Future<void> contributeToGoal(int goalId, double amount) async {
    _setLoading(true);
    _clearError();

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      // Find and update the goal
      final goalIndex = _savingsGoals.indexWhere((goal) => goal['id'] == goalId);
      if (goalIndex != -1) {
        _savingsGoals[goalIndex]['current_amount'] += amount;
        _savingsGoals[goalIndex]['progress'] = 
            _savingsGoals[goalIndex]['current_amount'] / _savingsGoals[goalIndex]['target_amount'];
        
        _currentBalance -= amount;
        
        // Add to recent transactions
        _recentTransactions.insert(0, {
          'id': DateTime.now().millisecondsSinceEpoch,
          'type': 'goal_contribution',
          'amount': amount,
          'description': 'Goal: ${_savingsGoals[goalIndex]['title']}',
          'date': DateTime.now(),
        });
      }
      
      _setLoading(false);
    } catch (e) {
      _setError('Failed to contribute to goal: ${e.toString()}');
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }
}
