apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-deployment
  namespace: bloom-budget
  labels:
    app: backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: ghcr.io/alinma/bloom-budget:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          value: "mysql+mysqlconnector://$(DB_USER):$(DB_PASSWORD)@$(DB_HOST):$(DB_PORT)/$(DB_NAME)"
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: bloom-budget-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: bloom-budget-config
              key: DB_PORT
        - name: DB_NAME
          valueFrom:
            configMapKeyRef:
              name: bloom-budget-config
              key: DB_NAME
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: bloom-budget-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: bloom-budget-secrets
              key: DB_PASSWORD
        - name: REDIS_URL
          value: "redis://$(REDIS_HOST):$(REDIS_PORT)/0"
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: bloom-budget-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: bloom-budget-config
              key: REDIS_PORT
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: bloom-budget-secrets
              key: SECRET_KEY
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: bloom-budget-secrets
              key: OPENAI_API_KEY
        - name: DEBUG
          valueFrom:
            configMapKeyRef:
              name: bloom-budget-config
              key: DEBUG
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: uploads-volume
          mountPath: /app/uploads
        - name: logs-volume
          mountPath: /app/logs
      volumes:
      - name: uploads-volume
        persistentVolumeClaim:
          claimName: uploads-pvc
      - name: logs-volume
        persistentVolumeClaim:
          claimName: logs-pvc
      imagePullSecrets:
      - name: ghcr-secret

---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: bloom-budget
  labels:
    app: backend
spec:
  selector:
    app: backend
  ports:
    - protocol: TCP
      port: 8000
      targetPort: 8000
  type: ClusterIP

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: uploads-pvc
  namespace: bloom-budget
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: nfs

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: logs-pvc
  namespace: bloom-budget
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
  storageClassName: nfs

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-hpa
  namespace: bloom-budget
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend-deployment
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
