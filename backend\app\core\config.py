from pydantic_settings import BaseSettings
from typing import List
import os

class Settings(BaseSettings):
    # App Configuration
    APP_NAME: str = "BloomBudget"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = True
    
    # Database Configuration
    DATABASE_URL: str = "mysql+mysqlconnector://root:password@localhost:3306/bloom_budget_db"
    DB_HOST: str = "localhost"
    DB_PORT: int = 3306
    DB_USER: str = "root"
    DB_PASSWORD: str = "password"
    DB_NAME: str = "bloom_budget_db"
    
    # JWT Configuration
    SECRET_KEY: str = "your-secret-key-change-this-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Redis Configuration
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # OpenAI Configuration
    OPENAI_API_KEY: str = ""
    
    # Email Configuration
    SMTP_HOST: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    SMTP_USER: str = ""
    SMTP_PASSWORD: str = ""
    
    # CORS Configuration
    CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # File Upload Configuration
    MAX_FILE_SIZE: int = 10485760  # 10MB
    UPLOAD_DIR: str = "uploads/"
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    
    # Rose Garden Configuration
    BASIC_ROSE_THRESHOLD: int = 100
    PREMIUM_ROSE_THRESHOLD: int = 500
    LUXURY_ROSE_THRESHOLD: int = 1000
    
    # Digital Safe Configuration
    MIN_SAFE_AMOUNT: int = 50
    MAX_SAFE_AMOUNT: int = 10000
    SAFE_GAME_ATTEMPTS: int = 3
    
    # Challenge Configuration
    MIN_CHALLENGE_PARTICIPANTS: int = 2
    MAX_CHALLENGE_PARTICIPANTS: int = 10
    CHALLENGE_DURATION_DAYS: int = 30

    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
