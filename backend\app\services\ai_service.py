import openai
import json
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from ..core.config import settings
from ..models.user import User
from ..models.savings_goal import SavingsGoal
from ..models.chat import ChatSession, ChatMessage, MessageType

class AIFinancialAdvisor:
    def __init__(self):
        openai.api_key = settings.OPENAI_API_KEY
        self.model = "gpt-3.5-turbo"
        
    def get_user_context(self, user: User, db) -> Dict:
        """Get user's financial context for AI analysis"""
        # Get user's savings goals
        goals = db.query(SavingsGoal).filter(SavingsGoal.user_id == user.id).all()
        
        # Get rose garden info
        rose_garden = user.rose_garden
        
        context = {
            "user_profile": {
                "name": user.full_name,
                "age": self._calculate_age(user.date_of_birth) if user.date_of_birth else None,
                "total_savings": user.total_savings,
                "current_balance": user.current_balance,
                "preferred_language": user.preferred_language
            },
            "savings_goals": [
                {
                    "title": goal.title,
                    "target_amount": goal.target_amount,
                    "current_amount": goal.current_amount,
                    "progress_percentage": goal.progress_percentage,
                    "days_remaining": goal.days_remaining,
                    "category": goal.category.value
                } for goal in goals
            ],
            "rose_garden": {
                "total_roses": rose_garden.total_roses if rose_garden else 0,
                "garden_level": rose_garden.garden_level if rose_garden else 1,
                "total_savings_contributed": rose_garden.total_savings_contributed if rose_garden else 0
            }
        }
        
        return context
    
    def _calculate_age(self, birth_date: datetime) -> int:
        """Calculate age from birth date"""
        today = datetime.now()
        return today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
    
    def generate_system_prompt(self, user_context: Dict) -> str:
        """Generate system prompt with user context"""
        return f"""
        You are a helpful financial advisor for BloomBudget, a savings app for Saudi youth. 
        You help users with financial planning, savings goals, and money management advice.
        
        User Context:
        - Name: {user_context['user_profile']['name']}
        - Total Savings: {user_context['user_profile']['total_savings']} SAR
        - Current Balance: {user_context['user_profile']['current_balance']} SAR
        - Active Goals: {len(user_context['savings_goals'])}
        - Rose Garden Level: {user_context['rose_garden']['garden_level']}
        
        Guidelines:
        1. Provide practical, actionable financial advice
        2. Consider Saudi Arabian context and culture
        3. Encourage savings habits and goal achievement
        4. Be supportive and motivational
        5. Use simple, clear language
        6. Suggest specific amounts and timeframes when relevant
        7. Reference their rose garden progress to motivate them
        
        Always respond in a helpful, encouraging tone and provide specific, actionable advice.
        """
    
    async def get_ai_response(self, message: str, user_context: Dict, conversation_history: List[Dict] = None) -> Dict:
        """Get AI response for user message"""
        try:
            messages = [
                {"role": "system", "content": self.generate_system_prompt(user_context)}
            ]
            
            # Add conversation history
            if conversation_history:
                messages.extend(conversation_history[-10:])  # Last 10 messages for context
            
            # Add current user message
            messages.append({"role": "user", "content": message})
            
            response = await openai.ChatCompletion.acreate(
                model=self.model,
                messages=messages,
                max_tokens=500,
                temperature=0.7
            )
            
            ai_message = response.choices[0].message.content
            tokens_used = response.usage.total_tokens
            
            return {
                "message": ai_message,
                "tokens_used": tokens_used,
                "model": self.model,
                "success": True
            }
            
        except Exception as e:
            return {
                "message": "I'm sorry, I'm having trouble processing your request right now. Please try again later.",
                "error": str(e),
                "success": False
            }
    
    def analyze_spending_pattern(self, transactions: List, user_context: Dict) -> Dict:
        """Analyze user's spending patterns and provide insights"""
        if not transactions:
            return {"insights": "Not enough transaction data for analysis"}
        
        # Calculate spending by category
        spending_by_category = {}
        total_spending = 0
        
        for transaction in transactions:
            if transaction.transaction_type == "withdrawal":
                category = transaction.category or "other"
                spending_by_category[category] = spending_by_category.get(category, 0) + transaction.amount
                total_spending += transaction.amount
        
        # Generate insights
        insights = {
            "total_spending": total_spending,
            "spending_by_category": spending_by_category,
            "recommendations": []
        }
        
        # Add recommendations based on spending patterns
        if total_spending > user_context['user_profile']['total_savings'] * 0.8:
            insights["recommendations"].append("Consider reducing spending to increase your savings rate")
        
        if "entertainment" in spending_by_category and spending_by_category["entertainment"] > total_spending * 0.3:
            insights["recommendations"].append("Entertainment spending is high. Consider setting a monthly budget for fun activities")
        
        return insights
    
    def suggest_savings_plan(self, goal_amount: float, target_date: datetime, current_savings: float) -> Dict:
        """Suggest a savings plan for a specific goal"""
        days_remaining = (target_date - datetime.now()).days
        remaining_amount = goal_amount - current_savings
        
        if days_remaining <= 0:
            return {"error": "Target date has passed"}
        
        daily_savings_needed = remaining_amount / days_remaining
        weekly_savings_needed = daily_savings_needed * 7
        monthly_savings_needed = daily_savings_needed * 30
        
        plan = {
            "remaining_amount": remaining_amount,
            "days_remaining": days_remaining,
            "daily_savings_needed": round(daily_savings_needed, 2),
            "weekly_savings_needed": round(weekly_savings_needed, 2),
            "monthly_savings_needed": round(monthly_savings_needed, 2),
            "recommendations": []
        }
        
        # Add recommendations based on required savings rate
        if daily_savings_needed > 100:
            plan["recommendations"].append("This goal requires high daily savings. Consider extending the timeline or reducing the target amount.")
        elif daily_savings_needed < 10:
            plan["recommendations"].append("This goal is very achievable! Consider setting additional goals to maximize your savings potential.")
        else:
            plan["recommendations"].append("This goal looks realistic. Stay consistent with your daily savings to achieve it!")
        
        return plan
    
    def generate_motivational_message(self, user_context: Dict) -> str:
        """Generate a motivational message based on user's progress"""
        total_roses = user_context['rose_garden']['total_roses']
        garden_level = user_context['rose_garden']['garden_level']
        active_goals = len(user_context['savings_goals'])
        
        messages = [
            f"🌹 Amazing! You have {total_roses} roses in your garden! Keep saving to grow more beautiful flowers!",
            f"🎯 You're doing great with {active_goals} active savings goals! Stay focused and you'll achieve them all!",
            f"⭐ Level {garden_level} gardener! Your dedication to saving is truly inspiring!",
            f"💪 Every SAR you save brings you closer to your dreams. Keep up the excellent work!",
            f"🌟 Your rose garden is blooming beautifully! Each rose represents your commitment to a better financial future!"
        ]
        
        import random
        return random.choice(messages)

# Initialize the AI service
ai_advisor = AIFinancialAdvisor()
