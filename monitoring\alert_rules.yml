groups:
- name: bloom_budget_alerts
  rules:
  # High CPU Usage
  - alert: HighCPUUsage
    expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage detected"
      description: "CPU usage is above 80% for more than 5 minutes on {{ $labels.instance }}"

  # High Memory Usage
  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage detected"
      description: "Memory usage is above 85% for more than 5 minutes on {{ $labels.instance }}"

  # Backend Service Down
  - alert: BackendServiceDown
    expr: up{job="bloom-budget-backend"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "BloomBudget backend service is down"
      description: "The BloomBudget backend service has been down for more than 1 minute"

  # Database Connection Issues
  - alert: DatabaseConnectionIssues
    expr: mysql_up == 0
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "MySQL database is unreachable"
      description: "MySQL database has been unreachable for more than 2 minutes"

  # High API Response Time
  - alert: HighAPIResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="bloom-budget-backend"}[5m])) > 2
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High API response time"
      description: "95th percentile API response time is above 2 seconds for more than 5 minutes"

  # High Error Rate
  - alert: HighErrorRate
    expr: rate(http_requests_total{job="bloom-budget-backend",status=~"5.."}[5m]) / rate(http_requests_total{job="bloom-budget-backend"}[5m]) > 0.05
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High error rate detected"
      description: "Error rate is above 5% for more than 5 minutes"

  # Disk Space Low
  - alert: DiskSpaceLow
    expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Low disk space"
      description: "Disk space is below 10% on {{ $labels.instance }}"

  # Redis Down
  - alert: RedisDown
    expr: redis_up == 0
    for: 1m
    labels:
      severity: warning
    annotations:
      summary: "Redis is down"
      description: "Redis has been down for more than 1 minute"

  # Too Many Failed Login Attempts
  - alert: TooManyFailedLogins
    expr: increase(http_requests_total{job="bloom-budget-backend",endpoint="/api/v1/auth/login",status="401"}[5m]) > 50
    for: 1m
    labels:
      severity: warning
    annotations:
      summary: "Too many failed login attempts"
      description: "More than 50 failed login attempts in the last 5 minutes"

  # Pod Restart Loop
  - alert: PodRestartLoop
    expr: increase(kube_pod_container_status_restarts_total[1h]) > 5
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Pod is restarting frequently"
      description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} has restarted more than 5 times in the last hour"

  # Certificate Expiry
  - alert: CertificateExpiringSoon
    expr: probe_ssl_earliest_cert_expiry - time() < 86400 * 30
    for: 1h
    labels:
      severity: warning
    annotations:
      summary: "SSL certificate expiring soon"
      description: "SSL certificate for {{ $labels.instance }} expires in less than 30 days"
