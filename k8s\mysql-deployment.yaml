apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mysql-pvc
  namespace: bloom-budget
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: fast-ssd

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql-deployment
  namespace: bloom-budget
  labels:
    app: mysql
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        ports:
        - containerPort: 3306
        env:
        - name: MYSQL_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: bloom-budget-secrets
              key: MYSQL_ROOT_PASSWORD
        - name: MYSQL_DATABASE
          valueFrom:
            configMapKeyRef:
              name: bloom-budget-config
              key: DB_NAME
        - name: MYSQL_USER
          valueFrom:
            secretKeyRef:
              name: bloom-budget-secrets
              key: DB_USER
        - name: <PERSON>YS<PERSON>_PASSWORD
          valueFrom:
            secretKeyRef:
              name: bloom-budget-secrets
              key: DB_PASSWORD
        volumeMounts:
        - name: mysql-storage
          mountPath: /var/lib/mysql
        - name: mysql-init
          mountPath: /docker-entrypoint-initdb.d
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          exec:
            command:
            - mysqladmin
            - ping
            - -h
            - localhost
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        readinessProbe:
          exec:
            command:
            - mysqladmin
            - ping
            - -h
            - localhost
          initialDelaySeconds: 5
          periodSeconds: 2
          timeoutSeconds: 1
      volumes:
      - name: mysql-storage
        persistentVolumeClaim:
          claimName: mysql-pvc
      - name: mysql-init
        configMap:
          name: mysql-init-config

---
apiVersion: v1
kind: Service
metadata:
  name: mysql-service
  namespace: bloom-budget
spec:
  selector:
    app: mysql
  ports:
    - protocol: TCP
      port: 3306
      targetPort: 3306
  type: ClusterIP

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: mysql-init-config
  namespace: bloom-budget
data:
  init.sql: |
    -- BloomBudget Database Initialization
    CREATE DATABASE IF NOT EXISTS bloom_budget_db 
    CHARACTER SET utf8mb4 
    COLLATE utf8mb4_unicode_ci;
    
    USE bloom_budget_db;
    
    -- Create indexes for better performance
    -- These will be created by SQLAlchemy migrations
