version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: bloom_budget_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-rootpassword}
      MYSQL_DATABASE: ${DB_NAME:-bloom_budget_db}
      MYSQL_USER: ${DB_USER:-bloom_budget_user}
      MYSQL_PASSWORD: ${DB_PASSWORD:-secure_password_123}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backend/create_database.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - bloom_budget_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: bloom_budget_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - bloom_budget_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: bloom_budget_backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql+mysqlconnector://${DB_USER:-bloom_budget_user}:${DB_PASSWORD:-secure_password_123}@mysql:3306/${DB_NAME:-bloom_budget_db}
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-change-this-in-production}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DEBUG=${DEBUG:-False}
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - bloom_budget_network
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: bloom_budget_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - backend
    networks:
      - bloom_budget_network

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: bloom_budget_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - bloom_budget_network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: bloom_budget_grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - bloom_budget_network

  # Log Management with ELK Stack
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: bloom_budget_elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - bloom_budget_network

  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: bloom_budget_logstash
    restart: unless-stopped
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
      - ./backend/logs:/usr/share/logstash/logs
    ports:
      - "5044:5044"
    environment:
      LS_JAVA_OPTS: "-Xmx256m -Xms256m"
    depends_on:
      - elasticsearch
    networks:
      - bloom_budget_network

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: bloom_budget_kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - bloom_budget_network

volumes:
  mysql_data:
  redis_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:

networks:
  bloom_budget_network:
    driver: bridge
