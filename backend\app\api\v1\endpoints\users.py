from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from ....core.database import get_db
from ....models.user import User
from ....schemas.auth import UserResponse
from .auth import get_current_user

router = APIRouter()

@router.get("/profile", response_model=UserResponse)
def get_user_profile(current_user: User = Depends(get_current_user)):
    """Get current user's profile"""
    return current_user

@router.put("/profile", response_model=UserResponse)
def update_user_profile(
    full_name: str = None,
    phone_number: str = None,
    preferred_language: str = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update current user's profile"""
    if full_name is not None:
        current_user.full_name = full_name
    if phone_number is not None:
        current_user.phone_number = phone_number
    if preferred_language is not None:
        current_user.preferred_language = preferred_language
    
    db.commit()
    db.refresh(current_user)
    return current_user

@router.get("/dashboard")
def get_user_dashboard(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user dashboard data"""
    # Get user's savings goals
    savings_goals = db.query(current_user.savings_goals).all()
    
    # Get rose garden info
    rose_garden = current_user.rose_garden
    
    # Get active challenges
    active_challenges = [p.challenge for p in current_user.challenge_participations 
                        if p.is_active and p.challenge.status == "active"]
    
    # Get recent transactions
    recent_transactions = db.query(current_user.transactions).order_by(
        current_user.transactions.created_at.desc()
    ).limit(10).all()
    
    return {
        "user": current_user,
        "total_savings": current_user.total_savings,
        "current_balance": current_user.current_balance,
        "savings_goals_count": len(savings_goals),
        "active_goals": [g for g in savings_goals if g.status == "active"],
        "rose_garden": {
            "total_roses": rose_garden.total_roses if rose_garden else 0,
            "garden_level": rose_garden.garden_level if rose_garden else 1,
            "basic_roses": rose_garden.basic_roses if rose_garden else 0,
            "premium_roses": rose_garden.premium_roses if rose_garden else 0,
            "luxury_roses": rose_garden.luxury_roses if rose_garden else 0,
        },
        "active_challenges_count": len(active_challenges),
        "recent_transactions": recent_transactions[:5]
    }

@router.delete("/account")
def delete_user_account(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete user account (soft delete)"""
    current_user.is_active = False
    db.commit()
    return {"message": "Account deactivated successfully"}
