import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';

class LocalStorage {
  static LocalStorage? _instance;
  static SharedPreferences? _prefs;

  LocalStorage._internal();

  static LocalStorage get instance {
    _instance ??= LocalStorage._internal();
    return _instance!;
  }

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Generic methods
  Future<bool> setString(String key, String value) async {
    return await _prefs!.setString(key, value);
  }

  String? getString(String key) {
    return _prefs!.getString(key);
  }

  Future<bool> setInt(String key, int value) async {
    return await _prefs!.setInt(key, value);
  }

  int? getInt(String key) {
    return _prefs!.getInt(key);
  }

  Future<bool> setBool(String key, bool value) async {
    return await _prefs!.setBool(key, value);
  }

  bool? getBool(String key) {
    return _prefs!.getBool(key);
  }

  Future<bool> setDouble(String key, double value) async {
    return await _prefs!.setDouble(key, value);
  }

  double? getDouble(String key) {
    return _prefs!.getDouble(key);
  }

  Future<bool> setObject(String key, Map<String, dynamic> value) async {
    return await _prefs!.setString(key, json.encode(value));
  }

  Map<String, dynamic>? getObject(String key) {
    final jsonString = _prefs!.getString(key);
    if (jsonString != null) {
      return json.decode(jsonString) as Map<String, dynamic>;
    }
    return null;
  }

  Future<bool> remove(String key) async {
    return await _prefs!.remove(key);
  }

  Future<bool> clear() async {
    return await _prefs!.clear();
  }

  // App-specific methods
  Future<bool> setUserToken(String token) async {
    return await setString(AppConstants.userTokenKey, token);
  }

  String? getUserToken() {
    return getString(AppConstants.userTokenKey);
  }

  Future<bool> setUserData(Map<String, dynamic> userData) async {
    return await setObject(AppConstants.userDataKey, userData);
  }

  Map<String, dynamic>? getUserData() {
    return getObject(AppConstants.userDataKey);
  }

  Future<bool> setLanguage(String language) async {
    return await setString(AppConstants.languageKey, language);
  }

  String getLanguage() {
    return getString(AppConstants.languageKey) ?? 'en';
  }

  Future<bool> setTheme(String theme) async {
    return await setString(AppConstants.themeKey, theme);
  }

  String getTheme() {
    return getString(AppConstants.themeKey) ?? 'dark';
  }

  Future<bool> logout() async {
    await remove(AppConstants.userTokenKey);
    await remove(AppConstants.userDataKey);
    return true;
  }

  bool isLoggedIn() {
    return getUserToken() != null;
  }
}
