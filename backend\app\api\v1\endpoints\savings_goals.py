from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from datetime import datetime

from ....core.database import get_db
from ....models.user import User
from ....models.savings_goal import SavingsGoal, GoalStatus, GoalCategory
from ....schemas.savings_goal import SavingsGoalCreate, SavingsGoalUpdate, SavingsGoalResponse
from .auth import get_current_user

router = APIRouter()

@router.post("/", response_model=SavingsGoalResponse)
def create_savings_goal(
    goal_data: SavingsGoalCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new savings goal"""
    db_goal = SavingsGoal(
        user_id=current_user.id,
        title=goal_data.title,
        description=goal_data.description,
        category=goal_data.category,
        target_amount=goal_data.target_amount,
        target_date=goal_data.target_date,
        priority=goal_data.priority,
        goal_image=goal_data.goal_image,
        color_theme=goal_data.color_theme,
        is_public=goal_data.is_public
    )
    
    # Calculate weekly and monthly targets
    if goal_data.target_date:
        days_remaining = (goal_data.target_date - datetime.now()).days
        if days_remaining > 0:
            db_goal.weekly_target = goal_data.target_amount / (days_remaining / 7)
            db_goal.monthly_target = goal_data.target_amount / (days_remaining / 30)
    
    db.add(db_goal)
    db.commit()
    db.refresh(db_goal)
    
    return db_goal

@router.get("/", response_model=List[SavingsGoalResponse])
def get_savings_goals(
    status: GoalStatus = None,
    category: GoalCategory = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's savings goals"""
    query = db.query(SavingsGoal).filter(SavingsGoal.user_id == current_user.id)
    
    if status:
        query = query.filter(SavingsGoal.status == status)
    if category:
        query = query.filter(SavingsGoal.category == category)
    
    return query.order_by(SavingsGoal.priority.desc(), SavingsGoal.created_at.desc()).all()

@router.get("/{goal_id}", response_model=SavingsGoalResponse)
def get_savings_goal(
    goal_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific savings goal"""
    goal = db.query(SavingsGoal).filter(
        SavingsGoal.id == goal_id,
        SavingsGoal.user_id == current_user.id
    ).first()
    
    if not goal:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Savings goal not found"
        )
    
    return goal

@router.put("/{goal_id}", response_model=SavingsGoalResponse)
def update_savings_goal(
    goal_id: int,
    goal_update: SavingsGoalUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a savings goal"""
    goal = db.query(SavingsGoal).filter(
        SavingsGoal.id == goal_id,
        SavingsGoal.user_id == current_user.id
    ).first()
    
    if not goal:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Savings goal not found"
        )
    
    # Update fields
    update_data = goal_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(goal, field, value)
    
    # Recalculate targets if target_date or target_amount changed
    if 'target_date' in update_data or 'target_amount' in update_data:
        if goal.target_date:
            days_remaining = (goal.target_date - datetime.now()).days
            if days_remaining > 0:
                goal.weekly_target = goal.target_amount / (days_remaining / 7)
                goal.monthly_target = goal.target_amount / (days_remaining / 30)
    
    db.commit()
    db.refresh(goal)
    
    return goal

@router.post("/{goal_id}/contribute")
def contribute_to_goal(
    goal_id: int,
    amount: float,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Add money to a savings goal"""
    goal = db.query(SavingsGoal).filter(
        SavingsGoal.id == goal_id,
        SavingsGoal.user_id == current_user.id
    ).first()
    
    if not goal:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Savings goal not found"
        )
    
    if amount <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Amount must be positive"
        )
    
    # Update goal amount
    goal.current_amount += amount
    
    # Check if goal is completed
    if goal.current_amount >= goal.target_amount:
        goal.status = GoalStatus.COMPLETED
        goal.completed_date = datetime.utcnow()
    
    # Update user's total savings
    current_user.total_savings += amount
    current_user.current_balance += amount
    
    db.commit()
    
    return {
        "message": "Contribution added successfully",
        "goal": goal,
        "new_progress_percentage": goal.progress_percentage
    }

@router.delete("/{goal_id}")
def delete_savings_goal(
    goal_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a savings goal"""
    goal = db.query(SavingsGoal).filter(
        SavingsGoal.id == goal_id,
        SavingsGoal.user_id == current_user.id
    ).first()
    
    if not goal:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Savings goal not found"
        )
    
    db.delete(goal)
    db.commit()
    
    return {"message": "Savings goal deleted successfully"}
