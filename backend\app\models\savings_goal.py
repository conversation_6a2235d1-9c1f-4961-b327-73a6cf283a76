from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, Text, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..core.database import Base
import enum

class GoalStatus(enum.Enum):
    ACTIVE = "active"
    COMPLETED = "completed"
    PAUSED = "paused"
    CANCELLED = "cancelled"

class GoalCategory(enum.Enum):
    PHONE = "phone"
    TRAVEL = "travel"
    DEVICE = "device"
    EDUCATION = "education"
    EMERGENCY = "emergency"
    OTHER = "other"

class SavingsGoal(Base):
    __tablename__ = "savings_goals"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Goal details
    title = Column(String(100), nullable=False)
    description = Column(Text)
    category = Column(Enum(GoalCategory), default=GoalCategory.OTHER)
    target_amount = Column(Float, nullable=False)
    current_amount = Column(Float, default=0.0)
    
    # Timeline
    start_date = Column(DateTime(timezone=True), server_default=func.now())
    target_date = Column(DateTime(timezone=True), nullable=False)
    completed_date = Column(DateTime(timezone=True))
    
    # Status and settings
    status = Column(Enum(GoalStatus), default=GoalStatus.ACTIVE)
    is_public = Column(Boolean, default=False)  # For social sharing
    priority = Column(Integer, default=1)  # 1-5 priority level
    
    # Visual customization
    goal_image = Column(String(255))
    color_theme = Column(String(7))  # Hex color code
    
    # Progress tracking
    weekly_target = Column(Float)
    monthly_target = Column(Float)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="savings_goals")
    transactions = relationship("Transaction", back_populates="savings_goal")
    
    @property
    def progress_percentage(self):
        if self.target_amount == 0:
            return 0
        return min((self.current_amount / self.target_amount) * 100, 100)
    
    @property
    def remaining_amount(self):
        return max(self.target_amount - self.current_amount, 0)
    
    @property
    def days_remaining(self):
        if self.target_date:
            from datetime import datetime
            remaining = self.target_date - datetime.now(self.target_date.tzinfo)
            return max(remaining.days, 0)
        return 0

    def __repr__(self):
        return f"<SavingsGoal(id={self.id}, title='{self.title}', target_amount={self.target_amount})>"
