import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../providers/home_provider.dart';
import '../widgets/dashboard_card.dart';
import '../widgets/savings_goal_card.dart';
import '../widgets/transaction_item.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<HomeProvider>().loadDashboardData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Consumer<HomeProvider>(
        builder: (context, homeProvider, child) {
          if (homeProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppTheme.accentColor),
              ),
            );
          }

          return CustomScrollView(
            slivers: [
              // App Bar
              SliverAppBar(
                expandedHeight: 120,
                floating: false,
                pinned: true,
                backgroundColor: AppTheme.primaryColor,
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(
                    'BloomBudget',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  centerTitle: true,
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.notifications, color: Colors.white),
                    onPressed: () {
                      // TODO: Navigate to notifications
                    },
                  ),
                ],
              ),

              // Dashboard Cards
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Welcome message
                      Text(
                        'Welcome back!',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: AppTheme.primaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Dashboard overview
                      Row(
                        children: [
                          Expanded(
                            child: DashboardCard(
                              title: 'Total Savings',
                              value: '${homeProvider.totalSavings.toStringAsFixed(0)} SAR',
                              icon: Icons.savings,
                              color: AppTheme.successColor,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: DashboardCard(
                              title: 'Current Balance',
                              value: '${homeProvider.currentBalance.toStringAsFixed(0)} SAR',
                              icon: Icons.account_balance_wallet,
                              color: AppTheme.secondaryColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),

                      Row(
                        children: [
                          Expanded(
                            child: DashboardCard(
                              title: 'Active Goals',
                              value: '${homeProvider.activeGoalsCount}',
                              icon: Icons.flag,
                              color: AppTheme.warningColor,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: DashboardCard(
                              title: 'Rose Garden',
                              value: '${homeProvider.totalRoses} roses',
                              icon: Icons.local_florist,
                              color: AppTheme.basicRoseColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // Quick Actions
                      Text(
                        'Quick Actions',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: AppTheme.primaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 12),

                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () => _showAddSavingsDialog(context),
                              icon: const Icon(Icons.add),
                              label: const Text('Add Savings'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppTheme.primaryColor,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () {
                                // TODO: Navigate to create goal
                              },
                              icon: const Icon(Icons.flag),
                              label: const Text('New Goal'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppTheme.secondaryColor,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // Savings Goals
                      if (homeProvider.savingsGoals.isNotEmpty) ...[
                        Text(
                          'Your Goals',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            color: AppTheme.primaryTextColor,
                          ),
                        ),
                        const SizedBox(height: 12),
                        ...homeProvider.savingsGoals.map((goal) =>
                          Padding(
                            padding: const EdgeInsets.only(bottom: 12),
                            child: SavingsGoalCard(goal: goal),
                          ),
                        ),
                        const SizedBox(height: 24),
                      ],

                      // Recent Transactions
                      if (homeProvider.recentTransactions.isNotEmpty) ...[
                        Text(
                          'Recent Activity',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            color: AppTheme.primaryTextColor,
                          ),
                        ),
                        const SizedBox(height: 12),
                        ...homeProvider.recentTransactions.take(5).map((transaction) =>
                          TransactionItem(transaction: transaction),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddSavingsDialog(context),
        backgroundColor: AppTheme.accentColor,
        child: const Icon(Icons.add, color: Colors.black),
      ),
    );
  }

  void _showAddSavingsDialog(BuildContext context) {
    final TextEditingController amountController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppTheme.surfaceColor,
          title: Text(
            'Add Savings',
            style: TextStyle(color: AppTheme.primaryTextColor),
          ),
          content: TextField(
            controller: amountController,
            keyboardType: TextInputType.number,
            style: TextStyle(color: AppTheme.primaryTextColor),
            decoration: InputDecoration(
              labelText: 'Amount (SAR)',
              labelStyle: TextStyle(color: AppTheme.secondaryTextColor),
              border: OutlineInputBorder(
                borderSide: BorderSide(color: AppTheme.primaryColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: AppTheme.accentColor),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancel', style: TextStyle(color: AppTheme.secondaryTextColor)),
            ),
            ElevatedButton(
              onPressed: () {
                final amount = double.tryParse(amountController.text);
                if (amount != null && amount > 0) {
                  context.read<HomeProvider>().addSavings(amount);
                  Navigator.of(context).pop();
                }
              },
              style: ElevatedButton.styleFrom(backgroundColor: AppTheme.primaryColor),
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }
}
