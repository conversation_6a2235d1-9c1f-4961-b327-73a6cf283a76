apiVersion: v1
kind: Namespace
metadata:
  name: bloom-budget
  labels:
    name: bloom-budget
    environment: production
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: bloom-budget-quota
  namespace: bloom-budget
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "10"
    configmaps: "10"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: bloom-budget-limits
  namespace: bloom-budget
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "512Mi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
