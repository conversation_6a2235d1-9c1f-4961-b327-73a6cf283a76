from typing import List
from fastapi import API<PERSON>outer, Depends, HTTPException, status
from sqlalchemy.orm import Session
from datetime import datetime

from ....core.database import get_db
from ....models.user import User
from ....models.chat import ChatSession, ChatMessage, MessageType, ChatSessionStatus
from ....schemas.chat import (
    ChatSessionCreate, ChatSessionResponse, ChatMessageCreate, 
    ChatMessageResponse, AIAdviceRequest, AIAdviceResponse
)
from ....services.ai_service import ai_advisor
from .auth import get_current_user

router = APIRouter()

@router.post("/sessions", response_model=ChatSessionResponse)
def create_chat_session(
    session_data: ChatSessionCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new chat session"""
    db_session = ChatSession(
        user_id=current_user.id,
        session_title=session_data.session_title,
        session_type=session_data.session_type,
        context_data=session_data.context_data
    )
    
    db.add(db_session)
    db.commit()
    db.refresh(db_session)
    
    return db_session

@router.get("/sessions", response_model=List[ChatSessionResponse])
def get_chat_sessions(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's chat sessions"""
    sessions = db.query(ChatSession).filter(
        ChatSession.user_id == current_user.id
    ).order_by(ChatSession.last_activity.desc()).all()
    
    return sessions

@router.get("/sessions/{session_id}", response_model=ChatSessionResponse)
def get_chat_session(
    session_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific chat session"""
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat session not found"
        )
    
    return session

@router.post("/sessions/{session_id}/messages", response_model=ChatMessageResponse)
async def send_message(
    session_id: int,
    message_data: ChatMessageCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Send a message in a chat session"""
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat session not found"
        )
    
    # Create user message
    user_message = ChatMessage(
        session_id=session.id,
        message_type=MessageType.USER,
        content=message_data.content
    )
    
    db.add(user_message)
    session.message_count += 1
    session.last_activity = datetime.utcnow()
    
    # Get user context for AI
    user_context = ai_advisor.get_user_context(current_user, db)
    
    # Get conversation history
    conversation_history = []
    recent_messages = db.query(ChatMessage).filter(
        ChatMessage.session_id == session.id
    ).order_by(ChatMessage.created_at.desc()).limit(10).all()
    
    for msg in reversed(recent_messages):
        role = "user" if msg.message_type == MessageType.USER else "assistant"
        conversation_history.append({"role": role, "content": msg.content})
    
    # Get AI response
    ai_response = await ai_advisor.get_ai_response(
        message_data.content, 
        user_context, 
        conversation_history
    )
    
    # Create AI message
    ai_message = ChatMessage(
        session_id=session.id,
        message_type=MessageType.ASSISTANT,
        content=ai_response["message"],
        ai_model=ai_response.get("model"),
        tokens_used=ai_response.get("tokens_used"),
        response_time_ms=1000  # Placeholder
    )
    
    db.add(ai_message)
    session.message_count += 1
    session.total_tokens_used += ai_response.get("tokens_used", 0)
    
    db.commit()
    db.refresh(ai_message)
    
    return ai_message

@router.get("/sessions/{session_id}/messages", response_model=List[ChatMessageResponse])
def get_session_messages(
    session_id: int,
    limit: int = 50,
    offset: int = 0,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get messages from a chat session"""
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat session not found"
        )
    
    messages = db.query(ChatMessage).filter(
        ChatMessage.session_id == session.id
    ).order_by(ChatMessage.created_at.asc()).offset(offset).limit(limit).all()
    
    return messages

@router.post("/advice", response_model=AIAdviceResponse)
async def get_ai_advice(
    advice_request: AIAdviceRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get AI financial advice"""
    user_context = ai_advisor.get_user_context(current_user, db)
    
    # Create a specific prompt based on advice type
    if advice_request.advice_type == "savings_plan":
        prompt = f"I want to save {advice_request.target_amount} SAR by {advice_request.target_date}. I currently have {current_user.current_balance} SAR. Can you help me create a savings plan?"
    elif advice_request.advice_type == "goal_setting":
        prompt = f"I want to set a new savings goal for {advice_request.goal_description}. Can you help me determine a realistic target amount and timeline?"
    elif advice_request.advice_type == "spending_analysis":
        prompt = "Can you analyze my spending patterns and give me advice on how to save more money?"
    else:
        prompt = advice_request.question or "Can you give me general financial advice?"
    
    ai_response = await ai_advisor.get_ai_response(prompt, user_context)
    
    return {
        "advice": ai_response["message"],
        "advice_type": advice_request.advice_type,
        "confidence_score": 0.85,  # Placeholder
        "personalized": True,
        "follow_up_questions": [
            "Would you like me to help you create a specific savings plan?",
            "Do you have any other financial goals you'd like to discuss?",
            "Would you like tips on reducing expenses?"
        ]
    }

@router.post("/motivational-message")
def get_motivational_message(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a personalized motivational message"""
    user_context = ai_advisor.get_user_context(current_user, db)
    message = ai_advisor.generate_motivational_message(user_context)
    
    return {"message": message}

@router.put("/sessions/{session_id}/end")
def end_chat_session(
    session_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """End a chat session"""
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat session not found"
        )
    
    session.status = ChatSessionStatus.COMPLETED
    session.ended_at = datetime.utcnow()
    
    db.commit()
    
    return {"message": "Chat session ended successfully"}

@router.post("/messages/{message_id}/rate")
def rate_message(
    message_id: int,
    rating: int,
    feedback: str = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Rate an AI message"""
    if rating < 1 or rating > 5:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Rating must be between 1 and 5"
        )
    
    message = db.query(ChatMessage).join(ChatSession).filter(
        ChatMessage.id == message_id,
        ChatSession.user_id == current_user.id,
        ChatMessage.message_type == MessageType.ASSISTANT
    ).first()
    
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Message not found"
        )
    
    message.user_rating = rating
    message.user_feedback = feedback
    
    db.commit()
    
    return {"message": "Rating submitted successfully"}
