#!/bin/bash

# BloomBudget Restore Script
# This script restores database and files from backup

set -euo pipefail

# Configuration
BACKUP_DIR="/opt/backups/bloom-budget"
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-3306}"
DB_NAME="${DB_NAME:-bloom_budget_db}"
DB_USER="${DB_USER:-bloom_budget_user}"
DB_PASSWORD="${DB_PASSWORD:-secure_password_123}"
UPLOADS_DIR="${UPLOADS_DIR:-/opt/bloom-budget/uploads}"
SLACK_WEBHOOK="${SLACK_WEBHOOK:-}"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$BACKUP_DIR/restore.log"
}

# Slack notification function
notify_slack() {
    local message="$1"
    local color="${2:-good}"
    
    if [[ -n "$SLACK_WEBHOOK" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"attachments\":[{\"color\":\"$color\",\"text\":\"$message\"}]}" \
            "$SLACK_WEBHOOK" || true
    fi
}

# Error handler
error_handler() {
    local line_number=$1
    log "ERROR: Restore failed at line $line_number"
    notify_slack "❌ BloomBudget restore failed at line $line_number" "danger"
    exit 1
}

trap 'error_handler $LINENO' ERR

# Usage function
usage() {
    echo "Usage: $0 [OPTIONS] BACKUP_TIMESTAMP"
    echo ""
    echo "Options:"
    echo "  -d, --database-only    Restore only database"
    echo "  -f, --files-only       Restore only files"
    echo "  -c, --config-only      Restore only configuration"
    echo "  -y, --yes              Skip confirmation prompts"
    echo "  -s, --from-s3          Download backup from S3 first"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 20231201_143000                    # Restore all components"
    echo "  $0 -d 20231201_143000                 # Restore only database"
    echo "  $0 -f 20231201_143000                 # Restore only files"
    echo "  $0 -s -y 20231201_143000              # Download from S3 and restore without prompts"
    exit 1
}

# Parse command line arguments
DATABASE_ONLY=false
FILES_ONLY=false
CONFIG_ONLY=false
SKIP_CONFIRMATION=false
FROM_S3=false
BACKUP_TIMESTAMP=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--database-only)
            DATABASE_ONLY=true
            shift
            ;;
        -f|--files-only)
            FILES_ONLY=true
            shift
            ;;
        -c|--config-only)
            CONFIG_ONLY=true
            shift
            ;;
        -y|--yes)
            SKIP_CONFIRMATION=true
            shift
            ;;
        -s|--from-s3)
            FROM_S3=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            if [[ -z "$BACKUP_TIMESTAMP" ]]; then
                BACKUP_TIMESTAMP="$1"
            else
                echo "ERROR: Unknown option $1"
                usage
            fi
            shift
            ;;
    esac
done

# Validate arguments
if [[ -z "$BACKUP_TIMESTAMP" ]]; then
    echo "ERROR: Backup timestamp is required"
    usage
fi

# Validate timestamp format
if [[ ! "$BACKUP_TIMESTAMP" =~ ^[0-9]{8}_[0-9]{6}$ ]]; then
    echo "ERROR: Invalid timestamp format. Expected: YYYYMMDD_HHMMSS"
    exit 1
fi

BACKUP_NAME="bloom_budget_backup_$BACKUP_TIMESTAMP"

log "Starting BloomBudget restore process for backup: $BACKUP_NAME"

# Download from S3 if requested
if [[ "$FROM_S3" == "true" ]]; then
    if ! command -v aws &> /dev/null; then
        log "ERROR: AWS CLI not found but --from-s3 was specified"
        exit 1
    fi
    
    if [[ -z "${S3_BUCKET:-}" ]]; then
        log "ERROR: S3_BUCKET environment variable not set"
        exit 1
    fi
    
    log "Downloading backup from S3..."
    
    aws s3 cp "s3://$S3_BUCKET/database/${BACKUP_NAME}_database.sql.gz" "$BACKUP_DIR/" || true
    aws s3 cp "s3://$S3_BUCKET/files/${BACKUP_NAME}_files.tar.gz" "$BACKUP_DIR/" || true
    aws s3 cp "s3://$S3_BUCKET/config/${BACKUP_NAME}_config.tar.gz" "$BACKUP_DIR/" || true
    
    log "S3 download completed"
fi

# Define backup file paths
DB_BACKUP_FILE="$BACKUP_DIR/${BACKUP_NAME}_database.sql.gz"
FILES_BACKUP_FILE="$BACKUP_DIR/${BACKUP_NAME}_files.tar.gz"
CONFIG_BACKUP_FILE="$BACKUP_DIR/${BACKUP_NAME}_config.tar.gz"

# Check if backup files exist
if [[ "$DATABASE_ONLY" != "true" && "$FILES_ONLY" != "true" && "$CONFIG_ONLY" != "true" ]]; then
    # Full restore - check all files
    if [[ ! -f "$DB_BACKUP_FILE" ]]; then
        log "ERROR: Database backup file not found: $DB_BACKUP_FILE"
        exit 1
    fi
    
    if [[ ! -f "$CONFIG_BACKUP_FILE" ]]; then
        log "ERROR: Configuration backup file not found: $CONFIG_BACKUP_FILE"
        exit 1
    fi
    
    # Files backup is optional
    if [[ ! -f "$FILES_BACKUP_FILE" ]]; then
        log "WARNING: Files backup not found: $FILES_BACKUP_FILE"
    fi
else
    # Partial restore - check specific files
    if [[ "$DATABASE_ONLY" == "true" && ! -f "$DB_BACKUP_FILE" ]]; then
        log "ERROR: Database backup file not found: $DB_BACKUP_FILE"
        exit 1
    fi
    
    if [[ "$FILES_ONLY" == "true" && ! -f "$FILES_BACKUP_FILE" ]]; then
        log "ERROR: Files backup file not found: $FILES_BACKUP_FILE"
        exit 1
    fi
    
    if [[ "$CONFIG_ONLY" == "true" && ! -f "$CONFIG_BACKUP_FILE" ]]; then
        log "ERROR: Configuration backup file not found: $CONFIG_BACKUP_FILE"
        exit 1
    fi
fi

# Confirmation prompt
if [[ "$SKIP_CONFIRMATION" != "true" ]]; then
    echo ""
    echo "⚠️  WARNING: This will overwrite existing data!"
    echo ""
    echo "Restore details:"
    echo "  Backup timestamp: $BACKUP_TIMESTAMP"
    echo "  Database: $([ "$DATABASE_ONLY" == "true" ] || [ "$FILES_ONLY" != "true" ] && [ "$CONFIG_ONLY" != "true" ] && echo "YES" || echo "NO")"
    echo "  Files: $([ "$FILES_ONLY" == "true" ] || [ "$DATABASE_ONLY" != "true" ] && [ "$CONFIG_ONLY" != "true" ] && echo "YES" || echo "NO")"
    echo "  Configuration: $([ "$CONFIG_ONLY" == "true" ] || [ "$DATABASE_ONLY" != "true" ] && [ "$FILES_ONLY" != "true" ] && echo "YES" || echo "NO")"
    echo ""
    read -p "Are you sure you want to continue? (yes/no): " -r
    
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        log "Restore cancelled by user"
        exit 0
    fi
fi

# Stop services before restore
log "Stopping BloomBudget services..."
if command -v docker-compose &> /dev/null; then
    docker-compose -f /opt/bloom-budget/docker-compose.yml stop backend || true
elif command -v kubectl &> /dev/null; then
    kubectl scale deployment backend-deployment --replicas=0 -n bloom-budget || true
    sleep 10
fi

# 1. Database Restore
if [[ "$FILES_ONLY" != "true" && "$CONFIG_ONLY" != "true" ]]; then
    log "Restoring database..."
    
    # Verify backup integrity
    if ! gzip -t "$DB_BACKUP_FILE"; then
        log "ERROR: Database backup file is corrupted"
        exit 1
    fi
    
    # Create backup of current database
    CURRENT_BACKUP="$BACKUP_DIR/pre_restore_$(date +%Y%m%d_%H%M%S).sql"
    log "Creating backup of current database: $CURRENT_BACKUP"
    
    mysqldump \
        --host="$DB_HOST" \
        --port="$DB_PORT" \
        --user="$DB_USER" \
        --password="$DB_PASSWORD" \
        --single-transaction \
        --databases "$DB_NAME" \
        > "$CURRENT_BACKUP" || true
    
    # Restore database
    log "Restoring database from backup..."
    zcat "$DB_BACKUP_FILE" | mysql \
        --host="$DB_HOST" \
        --port="$DB_PORT" \
        --user="$DB_USER" \
        --password="$DB_PASSWORD"
    
    log "Database restore completed"
fi

# 2. Files Restore
if [[ "$DATABASE_ONLY" != "true" && "$CONFIG_ONLY" != "true" && -f "$FILES_BACKUP_FILE" ]]; then
    log "Restoring files..."
    
    # Verify backup integrity
    if ! tar -tzf "$FILES_BACKUP_FILE" > /dev/null; then
        log "ERROR: Files backup file is corrupted"
        exit 1
    fi
    
    # Create backup of current files
    if [[ -d "$UPLOADS_DIR" ]]; then
        CURRENT_FILES_BACKUP="$BACKUP_DIR/pre_restore_files_$(date +%Y%m%d_%H%M%S).tar.gz"
        log "Creating backup of current files: $CURRENT_FILES_BACKUP"
        tar -czf "$CURRENT_FILES_BACKUP" -C "$(dirname "$UPLOADS_DIR")" "$(basename "$UPLOADS_DIR")" || true
    fi
    
    # Restore files
    log "Restoring files from backup..."
    mkdir -p "$(dirname "$UPLOADS_DIR")"
    tar -xzf "$FILES_BACKUP_FILE" -C "$(dirname "$UPLOADS_DIR")"
    
    # Set proper permissions
    chown -R www-data:www-data "$UPLOADS_DIR" 2>/dev/null || true
    chmod -R 755 "$UPLOADS_DIR" 2>/dev/null || true
    
    log "Files restore completed"
fi

# 3. Configuration Restore
if [[ "$DATABASE_ONLY" != "true" && "$FILES_ONLY" != "true" ]]; then
    log "Restoring configuration..."
    
    # Verify backup integrity
    if ! tar -tzf "$CONFIG_BACKUP_FILE" > /dev/null; then
        log "ERROR: Configuration backup file is corrupted"
        exit 1
    fi
    
    # Create temporary directory for extraction
    TEMP_RESTORE_DIR=$(mktemp -d)
    trap "rm -rf $TEMP_RESTORE_DIR" EXIT
    
    # Extract configuration backup
    tar -xzf "$CONFIG_BACKUP_FILE" -C "$TEMP_RESTORE_DIR"
    
    # Restore configuration files (with caution)
    if [[ -f "$TEMP_RESTORE_DIR/docker-compose.yml" ]]; then
        cp "$TEMP_RESTORE_DIR/docker-compose.yml" "/opt/bloom-budget/docker-compose.yml.restored"
        log "Configuration restored to docker-compose.yml.restored (manual review required)"
    fi
    
    if [[ -d "$TEMP_RESTORE_DIR/nginx" ]]; then
        cp -r "$TEMP_RESTORE_DIR/nginx" "/opt/bloom-budget/nginx.restored"
        log "Nginx configuration restored to nginx.restored/ (manual review required)"
    fi
    
    log "Configuration restore completed (manual review required for sensitive files)"
fi

# Start services after restore
log "Starting BloomBudget services..."
if command -v docker-compose &> /dev/null; then
    docker-compose -f /opt/bloom-budget/docker-compose.yml up -d
elif command -v kubectl &> /dev/null; then
    kubectl scale deployment backend-deployment --replicas=3 -n bloom-budget
    sleep 30
fi

# Verify restore
log "Verifying restore..."
sleep 10

# Check database connectivity
if mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME; SHOW TABLES;" > /dev/null 2>&1; then
    log "Database connectivity: OK"
else
    log "ERROR: Database connectivity check failed"
    exit 1
fi

# Check API health
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    log "API health check: OK"
else
    log "WARNING: API health check failed (service may still be starting)"
fi

# Generate restore report
REPORT="✅ BloomBudget restore completed successfully!
📅 Restored from: $BACKUP_TIMESTAMP
💾 Database: $([ "$FILES_ONLY" != "true" ] && [ "$CONFIG_ONLY" != "true" ] && echo "✅" || echo "⏭️")
📁 Files: $([ "$DATABASE_ONLY" != "true" ] && [ "$CONFIG_ONLY" != "true" ] && [ -f "$FILES_BACKUP_FILE" ] && echo "✅" || echo "⏭️")
⚙️ Configuration: $([ "$DATABASE_ONLY" != "true" ] && [ "$FILES_ONLY" != "true" ] && echo "✅" || echo "⏭️")
🔍 Verification: Passed"

log "$REPORT"
notify_slack "$REPORT"

log "Restore process completed successfully!"

exit 0
