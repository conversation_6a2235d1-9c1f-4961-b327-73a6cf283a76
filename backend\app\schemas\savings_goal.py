from pydantic import BaseModel, validator
from typing import Optional
from datetime import datetime
from ..models.savings_goal import GoalStatus, GoalCategory

class SavingsGoalBase(BaseModel):
    title: str
    description: Optional[str] = None
    category: GoalCategory = GoalCategory.OTHER
    target_amount: float
    target_date: datetime
    priority: Optional[int] = 1
    goal_image: Optional[str] = None
    color_theme: Optional[str] = None
    is_public: Optional[bool] = False

class SavingsGoalCreate(SavingsGoalBase):
    @validator('target_amount')
    def validate_target_amount(cls, v):
        if v <= 0:
            raise ValueError('Target amount must be positive')
        return v
    
    @validator('target_date')
    def validate_target_date(cls, v):
        if v <= datetime.now():
            raise ValueError('Target date must be in the future')
        return v
    
    @validator('priority')
    def validate_priority(cls, v):
        if v is not None and (v < 1 or v > 5):
            raise ValueError('Priority must be between 1 and 5')
        return v

class SavingsGoalUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    category: Optional[GoalCategory] = None
    target_amount: Optional[float] = None
    target_date: Optional[datetime] = None
    priority: Optional[int] = None
    goal_image: Optional[str] = None
    color_theme: Optional[str] = None
    is_public: Optional[bool] = None
    status: Optional[GoalStatus] = None

class SavingsGoalResponse(SavingsGoalBase):
    id: int
    user_id: int
    current_amount: float
    status: GoalStatus
    start_date: datetime
    completed_date: Optional[datetime] = None
    weekly_target: Optional[float] = None
    monthly_target: Optional[float] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    progress_percentage: float
    remaining_amount: float
    days_remaining: int
    
    class Config:
        from_attributes = True
