#!/bin/bash

# Samsung Galaxy Note 10+ 5G (SM-N976U) Setup Script for Flutter Development

set -e

echo "🚀 Setting up Samsung Galaxy Note 10+ 5G for Flutter development..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Flutter is installed
check_flutter() {
    log "Checking Flutter installation..."
    if command -v flutter &> /dev/null; then
        success "Flutter is installed"
        flutter --version
    else
        error "Flutter is not installed. Please install Flutter first."
        echo "Visit: https://flutter.dev/docs/get-started/install"
        exit 1
    fi
}

# Check Android SDK
check_android_sdk() {
    log "Checking Android SDK..."
    if command -v adb &> /dev/null; then
        success "Android SDK is available"
        adb version
    else
        error "Android SDK not found. Please install Android Studio."
        exit 1
    fi
}

# Install Samsung USB drivers (Windows)
install_samsung_drivers() {
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        log "For Windows: Please download and install Samsung USB drivers"
        echo "Download from: https://developer.samsung.com/mobile/android-usb-driver.html"
        echo "Or install Samsung Smart Switch which includes drivers"
    fi
}

# Check device connection
check_device_connection() {
    log "Checking device connection..."
    
    # Kill and restart ADB server
    adb kill-server
    sleep 2
    adb start-server
    sleep 3
    
    # Check for devices
    devices=$(adb devices | grep -v "List of devices attached" | grep -v "^$")
    
    if [[ -z "$devices" ]]; then
        error "No devices found!"
        echo ""
        echo "Troubleshooting steps:"
        echo "1. Make sure USB debugging is enabled on your Samsung device"
        echo "2. Check that your USB cable supports data transfer (not just charging)"
        echo "3. Try a different USB port"
        echo "4. On your phone, check for USB debugging authorization popup"
        echo "5. Try 'adb kill-server && adb start-server'"
        return 1
    else
        success "Device(s) found:"
        echo "$devices"
        
        # Check specifically for SM-N976U
        if echo "$devices" | grep -q "device"; then
            success "Samsung Galaxy Note 10+ 5G is connected and ready!"
            return 0
        else
            warning "Device found but may not be authorized. Check your phone for USB debugging popup."
            return 1
        fi
    fi
}

# Setup Flutter for Samsung device
setup_flutter_samsung() {
    log "Setting up Flutter for Samsung Galaxy Note 10+ 5G..."
    
    # Accept Android licenses
    log "Accepting Android licenses..."
    flutter doctor --android-licenses || true
    
    # Run Flutter doctor
    log "Running Flutter doctor..."
    flutter doctor
    
    # Check for Samsung-specific issues
    log "Checking Samsung-specific configurations..."
    
    # Samsung devices sometimes need specific ADB settings
    adb shell settings put global development_settings_enabled 1 2>/dev/null || true
    adb shell settings put global adb_enabled 1 2>/dev/null || true
}

# Create device-specific configuration
create_device_config() {
    log "Creating Samsung Galaxy Note 10+ 5G configuration..."
    
    cat > samsung_n976u_config.properties << EOF
# Samsung Galaxy Note 10+ 5G (SM-N976U) Configuration
device.model=SM-N976U
device.name=Galaxy Note 10+ 5G
android.api.level=29
android.build.version=10
screen.density=516
screen.resolution=1440x3040
cpu.architecture=arm64-v8a

# Development settings
usb.debugging=true
stay.awake=true
oem.unlocking=true

# Performance settings
animation.scale=0.5
transition.scale=0.5
window.scale=0.5
EOF
    
    success "Device configuration created: samsung_n976u_config.properties"
}

# Test Flutter connection
test_flutter_connection() {
    log "Testing Flutter connection to Samsung device..."
    
    # Check Flutter devices
    log "Available Flutter devices:"
    flutter devices
    
    # Check if our Samsung device is listed
    if flutter devices | grep -i "SM-N976U\|Galaxy\|samsung" &> /dev/null; then
        success "Samsung Galaxy Note 10+ 5G detected by Flutter!"
        return 0
    else
        warning "Samsung device not detected by Flutter. Checking ADB..."
        
        # Check ADB devices
        if adb devices | grep "device$" &> /dev/null; then
            warning "Device visible to ADB but not Flutter. This might be normal."
            warning "Try running 'flutter run' anyway - it might work."
            return 0
        else
            error "Device not properly connected. Please check connection."
            return 1
        fi
    fi
}

# Install Samsung-specific tools
install_samsung_tools() {
    log "Installing Samsung-specific development tools..."
    
    # Samsung Android SDK extensions (if available)
    if command -v sdkmanager &> /dev/null; then
        log "Installing Samsung Android SDK components..."
        sdkmanager "platforms;android-29" "build-tools;29.0.3" "platform-tools" || true
    fi
}

# Main execution
main() {
    echo "🔧 Samsung Galaxy Note 10+ 5G Flutter Setup"
    echo "=========================================="
    echo ""
    
    check_flutter
    echo ""
    
    check_android_sdk
    echo ""
    
    install_samsung_drivers
    echo ""
    
    install_samsung_tools
    echo ""
    
    if check_device_connection; then
        echo ""
        setup_flutter_samsung
        echo ""
        create_device_config
        echo ""
        test_flutter_connection
        echo ""
        
        success "🎉 Samsung Galaxy Note 10+ 5G setup completed!"
        echo ""
        echo "Next steps:"
        echo "1. Navigate to your Flutter project: cd bloom_budget_app"
        echo "2. Get dependencies: flutter pub get"
        echo "3. Run the app: flutter run"
        echo ""
        echo "If you encounter issues, try:"
        echo "- flutter clean && flutter pub get"
        echo "- adb kill-server && adb start-server"
        echo "- Disconnect and reconnect your Samsung device"
        
    else
        error "Device setup failed. Please check the troubleshooting steps above."
        exit 1
    fi
}

# Run main function
main "$@"
