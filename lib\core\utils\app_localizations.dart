import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';

class AppLocalizations {
  final Locale locale;
  late Map<String, String> _localizedStrings;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  Future<bool> load() async {
    String jsonString = await rootBundle
        .loadString('assets/localization/${locale.languageCode}.json');
    Map<String, dynamic> jsonMap = json.decode(jsonString);

    _localizedStrings = jsonMap.map((key, value) {
      return MapEntry(key, value.toString());
    });

    return true;
  }

  String translate(String key) {
    return _localizedStrings[key] ?? key;
  }

  // Common translations
  String get appName => translate('app_name');
  String get welcome => translate('welcome');
  String get login => translate('login');
  String get register => translate('register');
  String get email => translate('email');
  String get password => translate('password');
  String get confirmPassword => translate('confirm_password');
  String get forgotPassword => translate('forgot_password');
  String get save => translate('save');
  String get cancel => translate('cancel');
  String get delete => translate('delete');
  String get edit => translate('edit');
  String get loading => translate('loading');
  String get error => translate('error');
  String get success => translate('success');
  String get retry => translate('retry');
  
  // BloomBudget specific
  String get roseGarden => translate('rose_garden');
  String get smartShoppingCart => translate('smart_shopping_cart');
  String get digitalSafe => translate('digital_safe');
  String get socialChallenges => translate('social_challenges');
  String get expertChat => translate('expert_chat');
  String get flexiblePlan => translate('flexible_plan');
  String get savingsGoal => translate('savings_goal');
  String get currentSavings => translate('current_savings');
  String get targetAmount => translate('target_amount');
  String get daysRemaining => translate('days_remaining');
  String get addMoney => translate('add_money');
  String get withdrawMoney => translate('withdraw_money');
  String get shareGarden => translate('share_garden');
  String get createChallenge => translate('create_challenge');
  String get joinChallenge => translate('join_challenge');
  String get unlockSafe => translate('unlock_safe');
  String get emergencyUnlock => translate('emergency_unlock');
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['en', 'ar'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    AppLocalizations localizations = AppLocalizations(locale);
    await localizations.load();
    return localizations;
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
