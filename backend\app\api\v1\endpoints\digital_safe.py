from typing import List
from fastapi import API<PERSON>outer, Depends, HTTPException, status
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import json
import random

from ....core.database import get_db
from ....models.user import User
from ....models.digital_safe import DigitalSafe, SafeUnlockAttempt, SafeStatus, GameType
from ....schemas.digital_safe import (
    DigitalSafeCreate, DigitalSafeResponse, SafeUnlockAttemptCreate, 
    SafeUnlockAttemptResponse, EmergencyUnlockRequest
)
from .auth import get_current_user

router = APIRouter()

def generate_game_data(game_type: GameType, difficulty: int) -> dict:
    """Generate game data based on type and difficulty"""
    if game_type == GameType.MATH_PUZZLE:
        if difficulty == 1:
            a, b = random.randint(1, 10), random.randint(1, 10)
            return {"question": f"{a} + {b}", "answer": a + b}
        elif difficulty == 2:
            a, b = random.randint(10, 50), random.randint(10, 50)
            return {"question": f"{a} - {b}", "answer": a - b}
        else:
            a, b = random.randint(2, 12), random.randint(2, 12)
            return {"question": f"{a} × {b}", "answer": a * b}
    
    elif game_type == GameType.MEMORY_GAME:
        sequence_length = 3 + difficulty
        sequence = [random.randint(1, 4) for _ in range(sequence_length)]
        return {"sequence": sequence, "length": sequence_length}
    
    elif game_type == GameType.PATTERN_MATCH:
        patterns = ["circle", "square", "triangle", "diamond"]
        pattern_length = 2 + difficulty
        pattern = [random.choice(patterns) for _ in range(pattern_length)]
        return {"pattern": pattern, "length": pattern_length}
    
    elif game_type == GameType.TRIVIA_QUESTION:
        questions = [
            {"question": "What is the capital of Saudi Arabia?", "answer": "Riyadh"},
            {"question": "What currency is used in Saudi Arabia?", "answer": "SAR"},
            {"question": "What is 2030 vision about?", "answer": "Saudi transformation"},
        ]
        return random.choice(questions)
    
    return {}

@router.post("/", response_model=DigitalSafeResponse)
def create_digital_safe(
    safe_data: DigitalSafeCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new digital safe"""
    if safe_data.locked_amount <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Locked amount must be positive"
        )
    
    if current_user.current_balance < safe_data.locked_amount:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Insufficient balance"
        )
    
    # Generate game data
    game_data = generate_game_data(safe_data.game_type, safe_data.game_difficulty)
    
    # Calculate unlock available date
    unlock_date = datetime.utcnow() + timedelta(days=safe_data.lock_duration_days)
    
    db_safe = DigitalSafe(
        user_id=current_user.id,
        safe_name=safe_data.safe_name,
        locked_amount=safe_data.locked_amount,
        lock_duration_days=safe_data.lock_duration_days,
        game_type=safe_data.game_type,
        game_difficulty=safe_data.game_difficulty,
        game_data=json.dumps(game_data),
        unlock_available_at=unlock_date,
        safe_color=safe_data.safe_color,
        safe_icon=safe_data.safe_icon
    )
    
    # Deduct amount from user's balance
    current_user.current_balance -= safe_data.locked_amount
    
    db.add(db_safe)
    db.commit()
    db.refresh(db_safe)
    
    return db_safe

@router.get("/", response_model=List[DigitalSafeResponse])
def get_digital_safes(
    status: SafeStatus = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's digital safes"""
    query = db.query(DigitalSafe).filter(DigitalSafe.user_id == current_user.id)
    
    if status:
        query = query.filter(DigitalSafe.status == status)
    
    return query.order_by(DigitalSafe.created_at.desc()).all()

@router.get("/{safe_id}", response_model=DigitalSafeResponse)
def get_digital_safe(
    safe_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific digital safe"""
    safe = db.query(DigitalSafe).filter(
        DigitalSafe.id == safe_id,
        DigitalSafe.user_id == current_user.id
    ).first()
    
    if not safe:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Digital safe not found"
        )
    
    return safe

@router.post("/{safe_id}/unlock-attempt", response_model=SafeUnlockAttemptResponse)
def attempt_unlock_safe(
    safe_id: int,
    attempt_data: SafeUnlockAttemptCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Attempt to unlock a digital safe"""
    safe = db.query(DigitalSafe).filter(
        DigitalSafe.id == safe_id,
        DigitalSafe.user_id == current_user.id
    ).first()
    
    if not safe:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Digital safe not found"
        )
    
    if not safe.is_unlockable:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Safe cannot be unlocked at this time"
        )
    
    # Check the answer
    game_data = json.loads(safe.game_data)
    is_correct = False
    
    if safe.game_type == GameType.MATH_PUZZLE:
        is_correct = attempt_data.answer == game_data.get("answer")
    elif safe.game_type == GameType.TRIVIA_QUESTION:
        is_correct = attempt_data.answer.lower() == game_data.get("answer", "").lower()
    # Add more game type checks as needed
    
    # Create attempt record
    attempt = SafeUnlockAttempt(
        safe_id=safe.id,
        attempt_number=safe.unlock_attempts + 1,
        game_type=safe.game_type,
        was_successful=is_correct,
        score=100 if is_correct else 0,
        time_taken_seconds=attempt_data.time_taken_seconds,
        game_result_data=json.dumps(attempt_data.dict())
    )
    
    safe.unlock_attempts += 1
    
    if is_correct:
        # Unlock the safe
        safe.status = SafeStatus.UNLOCKED
        safe.unlocked_at = datetime.utcnow()
        
        # Return money to user's balance
        current_user.current_balance += safe.locked_amount
        
        message = "Safe unlocked successfully!"
    else:
        message = f"Incorrect answer. {safe.max_attempts - safe.unlock_attempts} attempts remaining."
        
        if safe.unlock_attempts >= safe.max_attempts:
            message = "Maximum attempts reached. Safe remains locked until time expires."
    
    db.add(attempt)
    db.commit()
    db.refresh(attempt)
    
    return {
        "attempt": attempt,
        "message": message,
        "safe_unlocked": is_correct,
        "remaining_attempts": max(0, safe.max_attempts - safe.unlock_attempts)
    }

@router.post("/{safe_id}/emergency-unlock")
def request_emergency_unlock(
    safe_id: int,
    request_data: EmergencyUnlockRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Request emergency unlock for a digital safe"""
    safe = db.query(DigitalSafe).filter(
        DigitalSafe.id == safe_id,
        DigitalSafe.user_id == current_user.id
    ).first()
    
    if not safe:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Digital safe not found"
        )
    
    if safe.status != SafeStatus.LOCKED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Safe is not locked"
        )
    
    safe.emergency_unlock_requested = True
    safe.emergency_unlock_reason = request_data.reason
    
    db.commit()
    
    return {
        "message": "Emergency unlock request submitted. Support team will review your request.",
        "request_id": safe.id
    }

@router.delete("/{safe_id}")
def delete_digital_safe(
    safe_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a digital safe (only if unlocked)"""
    safe = db.query(DigitalSafe).filter(
        DigitalSafe.id == safe_id,
        DigitalSafe.user_id == current_user.id
    ).first()
    
    if not safe:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Digital safe not found"
        )
    
    if safe.status == SafeStatus.LOCKED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete a locked safe"
        )
    
    db.delete(safe)
    db.commit()
    
    return {"message": "Digital safe deleted successfully"}
