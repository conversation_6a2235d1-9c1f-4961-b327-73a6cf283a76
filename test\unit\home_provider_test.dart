import 'package:flutter_test/flutter_test.dart';
import 'package:bloom_budget_app/features/home/<USER>/providers/home_provider.dart';

void main() {
  group('HomeProvider Tests', () {
    late HomeProvider homeProvider;

    setUp(() {
      homeProvider = HomeProvider();
    });

    test('should initialize with correct default values', () {
      expect(homeProvider.selectedIndex, 0);
      expect(homeProvider.isLoading, false);
      expect(homeProvider.totalSavings, 0.0);
      expect(homeProvider.currentBalance, 0.0);
      expect(homeProvider.activeGoalsCount, 0);
      expect(homeProvider.totalRoses, 0);
      expect(homeProvider.gardenLevel, 1);
      expect(homeProvider.recentTransactions, isEmpty);
      expect(homeProvider.savingsGoals, isEmpty);
    });

    test('should set selected index correctly', () {
      homeProvider.setSelectedIndex(2);
      expect(homeProvider.selectedIndex, 2);
    });

    test('should load dashboard data successfully', () async {
      await homeProvider.loadDashboardData();
      
      expect(homeProvider.isLoading, false);
      expect(homeProvider.totalSavings, greaterThan(0));
      expect(homeProvider.currentBalance, greaterThan(0));
      expect(homeProvider.activeGoalsCount, greaterThan(0));
      expect(homeProvider.totalRoses, greaterThan(0));
      expect(homeProvider.gardenLevel, greaterThanOrEqualTo(1));
      expect(homeProvider.recentTransactions, isNotEmpty);
      expect(homeProvider.savingsGoals, isNotEmpty);
    });

    test('should add savings correctly', () async {
      // Load initial data
      await homeProvider.loadDashboardData();
      final initialBalance = homeProvider.currentBalance;
      final initialSavings = homeProvider.totalSavings;
      final initialRoses = homeProvider.totalRoses;
      
      // Add savings
      await homeProvider.addSavings(200.0);
      
      expect(homeProvider.currentBalance, initialBalance + 200.0);
      expect(homeProvider.totalSavings, initialSavings + 200.0);
      expect(homeProvider.totalRoses, greaterThanOrEqualTo(initialRoses));
      expect(homeProvider.recentTransactions.first['type'], 'deposit');
      expect(homeProvider.recentTransactions.first['amount'], 200.0);
    });

    test('should contribute to goal correctly', () async {
      // Load initial data
      await homeProvider.loadDashboardData();
      final initialBalance = homeProvider.currentBalance;
      final goalId = homeProvider.savingsGoals.first['id'];
      final initialGoalAmount = homeProvider.savingsGoals.first['current_amount'];
      
      // Contribute to goal
      await homeProvider.contributeToGoal(goalId, 100.0);
      
      expect(homeProvider.currentBalance, initialBalance - 100.0);
      
      final updatedGoal = homeProvider.savingsGoals.firstWhere((goal) => goal['id'] == goalId);
      expect(updatedGoal['current_amount'], initialGoalAmount + 100.0);
      expect(homeProvider.recentTransactions.first['type'], 'goal_contribution');
    });

    test('should clear error message', () {
      homeProvider.clearError();
      expect(homeProvider.errorMessage, null);
    });

    test('should increase garden level when roses threshold is reached', () async {
      await homeProvider.loadDashboardData();
      final initialLevel = homeProvider.gardenLevel;
      final initialRoses = homeProvider.totalRoses;
      
      // Add enough savings to potentially increase level
      // Assuming level increases every 10 roses
      final rosesNeeded = (initialLevel * 10) - initialRoses + 1;
      for (int i = 0; i < rosesNeeded; i++) {
        await homeProvider.addSavings(100.0); // Each 100+ SAR adds a rose
      }
      
      expect(homeProvider.gardenLevel, greaterThanOrEqualTo(initialLevel));
    });
  });
}
