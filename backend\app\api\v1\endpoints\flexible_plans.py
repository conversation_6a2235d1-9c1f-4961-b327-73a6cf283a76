from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db

router = APIRouter()

@router.get("/")
async def get_flexible_plans(db: Session = Depends(get_db)):
    """Get all flexible plans"""
    return {"message": "Flexible plans endpoint - coming soon"}

@router.post("/")
async def create_flexible_plan(db: Session = Depends(get_db)):
    """Create a new flexible plan"""
    return {"message": "Create flexible plan endpoint - coming soon"}

@router.get("/{plan_id}")
async def get_flexible_plan(plan_id: int, db: Session = Depends(get_db)):
    """Get a specific flexible plan"""
    return {"message": f"Flexible plan {plan_id} endpoint - coming soon"}
