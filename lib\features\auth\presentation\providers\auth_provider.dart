import 'package:flutter/material.dart';
import '../../../../core/storage/local_storage.dart';

class AuthProvider extends ChangeNotifier {
  bool _isLoading = false;
  bool _isAuthenticated = false;
  String? _token;
  Map<String, dynamic>? _userData;
  String? _errorMessage;

  // Getters
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _isAuthenticated;
  String? get token => _token;
  Map<String, dynamic>? get userData => _userData;
  String? get errorMessage => _errorMessage;

  AuthProvider() {
    _checkAuthStatus();
  }

  void _checkAuthStatus() {
    _token = LocalStorage.instance.getUserToken();
    _userData = LocalStorage.instance.getUserData();
    _isAuthenticated = _token != null;
    notifyListeners();
  }

  Future<bool> login(String username, String password) async {
    _setLoading(true);
    _clearError();

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      // Mock successful login
      if (username.isNotEmpty && password.isNotEmpty) {
        _token = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
        _userData = {
          'id': 1,
          'username': username,
          'email': '$<EMAIL>',
          'full_name': 'User Name',
          'total_savings': 0.0,
          'current_balance': 0.0,
        };
        
        await LocalStorage.instance.setUserToken(_token!);
        await LocalStorage.instance.setUserData(_userData!);
        
        _isAuthenticated = true;
        _setLoading(false);
        return true;
      } else {
        _setError('Invalid username or password');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Login failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> register(Map<String, dynamic> userData) async {
    _setLoading(true);
    _clearError();

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      // Mock successful registration
      _token = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
      _userData = {
        'id': 1,
        'username': userData['username'],
        'email': userData['email'],
        'full_name': userData['full_name'],
        'phone_number': userData['phone_number'],
        'total_savings': 0.0,
        'current_balance': 0.0,
      };
      
      await LocalStorage.instance.setUserToken(_token!);
      await LocalStorage.instance.setUserData(_userData!);
      
      _isAuthenticated = true;
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('Registration failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<void> logout() async {
    _setLoading(true);
    
    try {
      await LocalStorage.instance.logout();
      _token = null;
      _userData = null;
      _isAuthenticated = false;
      _clearError();
    } catch (e) {
      _setError('Logout failed: ${e.toString()}');
    }
    
    _setLoading(false);
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }
}
