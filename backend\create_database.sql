-- BloomBudget Database Creation Script
-- This script creates the MySQL database and user for the BloomBudget application

-- Create database
CREATE DATABASE IF NOT EXISTS bloom_budget_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Create user (replace 'password' with a strong password)
CREATE USER IF NOT EXISTS 'bloom_budget_user'@'localhost' IDENTIFIED BY 'secure_password_123';

-- Grant privileges
GRANT ALL PRIVILEGES ON bloom_budget_db.* TO 'bloom_budget_user'@'localhost';

-- Grant privileges for remote access (if needed)
CREATE USER IF NOT EXISTS 'bloom_budget_user'@'%' IDENTIFIED BY 'secure_password_123';
GRANT ALL PRIVILEGES ON bloom_budget_db.* TO 'bloom_budget_user'@'%';

-- Flush privileges
FLUSH PRIVILEGES;

-- Use the database
USE bloom_budget_db;

-- Create indexes for better performance
-- These will be created automatically by SQLAlchemy, but listed here for reference

-- User table indexes
-- CREATE INDEX idx_users_email ON users(email);
-- CREATE INDEX idx_users_username ON users(username);
-- CREATE INDEX idx_users_phone_number ON users(phone_number);
-- CREATE INDEX idx_users_created_at ON users(created_at);

-- Savings goals indexes
-- CREATE INDEX idx_savings_goals_user_id ON savings_goals(user_id);
-- CREATE INDEX idx_savings_goals_status ON savings_goals(status);
-- CREATE INDEX idx_savings_goals_target_date ON savings_goals(target_date);

-- Rose garden indexes
-- CREATE INDEX idx_rose_gardens_user_id ON rose_gardens(user_id);
-- CREATE INDEX idx_roses_garden_id ON roses(garden_id);

-- Digital safe indexes
-- CREATE INDEX idx_digital_safes_user_id ON digital_safes(user_id);
-- CREATE INDEX idx_digital_safes_status ON digital_safes(status);

-- Challenge indexes
-- CREATE INDEX idx_challenges_creator_id ON challenges(creator_id);
-- CREATE INDEX idx_challenges_status ON challenges(status);
-- CREATE INDEX idx_challenge_participants_challenge_id ON challenge_participants(challenge_id);
-- CREATE INDEX idx_challenge_participants_user_id ON challenge_participants(user_id);

-- Chat indexes
-- CREATE INDEX idx_chat_sessions_user_id ON chat_sessions(user_id);
-- CREATE INDEX idx_chat_messages_session_id ON chat_messages(session_id);

-- Transaction indexes
-- CREATE INDEX idx_transactions_user_id ON transactions(user_id);
-- CREATE INDEX idx_transactions_type ON transactions(transaction_type);
-- CREATE INDEX idx_transactions_status ON transactions(status);
-- CREATE INDEX idx_transactions_created_at ON transactions(created_at);

-- Show database info
SELECT 'Database bloom_budget_db created successfully!' as message;
SHOW DATABASES LIKE 'bloom_budget_db';
