from sqlalchemy import Column, Integer, String, DateTime, <PERSON>olean, Float, Text, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..core.database import Base
import enum

class PlanStatus(enum.Enum):
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    MODIFIED = "modified"
    CANCELLED = "cancelled"

class AdjustmentType(enum.Enum):
    EXTEND_TIMELINE = "extend_timeline"
    REDUCE_AMOUNT = "reduce_amount"
    CHANGE_FREQUENCY = "change_frequency"
    MODIFY_GOAL = "modify_goal"
    PAUSE_PLAN = "pause_plan"

class FlexiblePlan(Base):
    __tablename__ = "flexible_plans"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    savings_goal_id = Column(Integer, ForeignKey("savings_goals.id"))
    
    # Plan details
    plan_name = Column(String(100), nullable=False)
    original_target_amount = Column(Float, nullable=False)
    current_target_amount = Column(Float, nullable=False)
    
    # Timeline
    original_target_date = Column(DateTime(timezone=True), nullable=False)
    current_target_date = Column(DateTime(timezone=True), nullable=False)
    
    # Frequency settings
    original_frequency = Column(String(20), nullable=False)  # daily, weekly, monthly
    current_frequency = Column(String(20), nullable=False)
    original_amount_per_period = Column(Float, nullable=False)
    current_amount_per_period = Column(Float, nullable=False)
    
    # Plan status
    status = Column(Enum(PlanStatus), default=PlanStatus.ACTIVE)
    modification_count = Column(Integer, default=0)
    
    # AI recommendations
    ai_suggested_adjustments = Column(Text)  # JSON with AI suggestions
    last_ai_analysis = Column(DateTime(timezone=True))
    
    # Performance tracking
    adherence_percentage = Column(Float, default=100.0)
    missed_payments_count = Column(Integer, default=0)
    total_payments_made = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_payment_date = Column(DateTime(timezone=True))
    
    # Relationships
    user = relationship("User", back_populates="flexible_plans")
    savings_goal = relationship("SavingsGoal")
    adjustments = relationship("PlanAdjustment", back_populates="plan")
    
    @property
    def current_progress_percentage(self):
        """Calculate current progress towards the goal"""
        if self.current_target_amount == 0:
            return 0
        # This would need to be calculated based on actual savings
        return 0  # Placeholder
    
    @property
    def is_on_track(self):
        """Check if the plan is on track based on timeline and progress"""
        # Implementation would check current progress vs expected progress
        return self.adherence_percentage >= 80.0

    def __repr__(self):
        return f"<FlexiblePlan(id={self.id}, name='{self.plan_name}', user_id={self.user_id})>"

class PlanAdjustment(Base):
    __tablename__ = "plan_adjustments"

    id = Column(Integer, primary_key=True, index=True)
    plan_id = Column(Integer, ForeignKey("flexible_plans.id"), nullable=False)
    
    # Adjustment details
    adjustment_type = Column(Enum(AdjustmentType), nullable=False)
    reason = Column(Text, nullable=False)
    
    # Previous values
    previous_target_amount = Column(Float)
    previous_target_date = Column(DateTime(timezone=True))
    previous_frequency = Column(String(20))
    previous_amount_per_period = Column(Float)
    
    # New values
    new_target_amount = Column(Float)
    new_target_date = Column(DateTime(timezone=True))
    new_frequency = Column(String(20))
    new_amount_per_period = Column(Float)
    
    # Adjustment metadata
    was_ai_suggested = Column(Boolean, default=False)
    user_approved = Column(Boolean, default=True)
    impact_assessment = Column(Text)  # JSON with impact analysis
    
    # Timestamps
    suggested_at = Column(DateTime(timezone=True), server_default=func.now())
    applied_at = Column(DateTime(timezone=True))
    
    # Relationships
    plan = relationship("FlexiblePlan", back_populates="adjustments")

    def __repr__(self):
        return f"<PlanAdjustment(id={self.id}, type='{self.adjustment_type}', plan_id={self.plan_id})>"

class PlanPerformance(Base):
    __tablename__ = "plan_performance"

    id = Column(Integer, primary_key=True, index=True)
    plan_id = Column(Integer, ForeignKey("flexible_plans.id"), nullable=False)
    
    # Performance metrics
    period_start = Column(DateTime(timezone=True), nullable=False)
    period_end = Column(DateTime(timezone=True), nullable=False)
    
    expected_savings = Column(Float, nullable=False)
    actual_savings = Column(Float, nullable=False)
    variance = Column(Float)  # actual - expected
    variance_percentage = Column(Float)
    
    # Behavioral insights
    payment_consistency = Column(Float)  # 0-100 score
    early_payments_count = Column(Integer, default=0)
    late_payments_count = Column(Integer, default=0)
    missed_payments_count = Column(Integer, default=0)
    
    # AI analysis
    ai_insights = Column(Text)  # JSON with AI-generated insights
    recommended_adjustments = Column(Text)  # JSON with recommendations
    
    # Timestamps
    calculated_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    plan = relationship("FlexiblePlan")

    def __repr__(self):
        return f"<PlanPerformance(id={self.id}, plan_id={self.plan_id}, variance={self.variance})>"
